#!/usr/bin/.env python3
# -*- coding: utf-8 -*-
"""
Enhanced <PERSON>Chai<PERSON> 涨停侦探 (Enhanced <PERSON>Chain ZtDetective) v3.0
- AI增强: 真正的探索性分析，而非简单的数据查询
- 深度工具: 6个高级分析工具，赋予AI深度挖掘能力
- 智能推理: 多层次分析架构，从表面现象到深层逻辑
- 动态报告: AI根据发现的洞察自主组织报告结构
"""

import os
import pandas as pd
import re
from collections import defaultdict
from datetime import datetime, timedelta
import warnings
import logging
import requests
import json
import numpy as np
from itertools import combinations

# --- 核心依赖导入 ---
from langchain.agents import initialize_agent, AgentType
from langchain.tools import tool
from langchain_openai import ChatOpenAI
from dotenv import load_dotenv

warnings.filterwarnings('ignore')

# --- 配置区 ---
load_dotenv()

# SiliconFlow API配置
SILICONFLOW_MODEL_NAME = "deepseek-ai/deepseek-v3"
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

INVALID_API_KEYS_FILE = "siliconflow_invalid_api_keys.txt"
API_KEYS = os.getenv("SILICONFLOW_API_KEYS", "").split(",")
if not API_KEYS or not API_KEYS[0]:
    print("❌ 错误：未在 .env 文件中找到 SILICONFLOW_API_KEYS。")
    exit()

API_KEY_INDEX_FILE = "siliconflow_api_key_index.txt"
try:
    with open(API_KEY_INDEX_FILE, 'r') as f:
        API_KEY_INDEX = int(f.read().strip())
except (FileNotFoundError, ValueError):
    API_KEY_INDEX = 0

API_KEY_INDEX = API_KEY_INDEX % len(API_KEYS) if API_KEYS else 0

def get_current_api_key():
    """获取当前有效的API密钥"""
    global API_KEY_INDEX
    
    invalid_keys = set()
    if os.path.exists(INVALID_API_KEYS_FILE):
        with open(INVALID_API_KEYS_FILE, 'r', encoding='utf-8') as f:
            invalid_keys = {line.strip() for line in f if line.strip()}
    
    initial_index = API_KEY_INDEX
    while True:
        api_key = API_KEYS[API_KEY_INDEX]
        if api_key not in invalid_keys:
            API_KEY_INDEX = (API_KEY_INDEX + 1) % len(API_KEYS)
            with open(API_KEY_INDEX_FILE, 'w') as f:
                f.write(str(API_KEY_INDEX))
            return api_key
        
        API_KEY_INDEX = (API_KEY_INDEX + 1) % len(API_KEYS)
        if API_KEY_INDEX == initial_index:
            print("❌ 错误：所有API密钥均无效。")
            exit()

# 路径配置
ANALYSIS_DATE = "2025-07-25"
FUND_DATA_DIR = "../fund_data"
POST_MARKET_DATA_DIR = "D:\\stock_dev\\zt_any\\data"
OUTPUT_DIR = "../reports_enhanced_langchain"

class EnhancedZtToolbox:
    """
    增强版工具箱 - 提供深度数据挖掘和关联分析能力
    """
    
    def __init__(self, analysis_date, data_dir, post_market_data_dir):
        print("🔧 初始化增强版工具箱...")
        self.analysis_date = analysis_date
        self.data_dir = os.path.join(data_dir, analysis_date)
        self.post_market_data_dir = os.path.join(post_market_data_dir, analysis_date.replace('-', ''))
        
        # 昨日数据路径
        yesterday = datetime.strptime(analysis_date, '%Y-%m-%d') - timedelta(days=1)
        self.yesterday_str_hyphen = yesterday.strftime('%Y-%m-%d')
        self.yesterday_data_dir = os.path.join(data_dir, self.yesterday_str_hyphen)
        self.yesterday_post_market_data_dir = os.path.join(post_market_data_dir, yesterday.strftime('%Y%m%d'))
        
        # 数据存储
        self.all_day_data = defaultdict(lambda: defaultdict(list))
        self.post_market_data = {}
        self.yesterday_data = {'intraday': defaultdict(list), 'post_market': {}}
        self.stock_info_map = {}
        
        # 新增：关系图谱和模式识别
        self.stock_relationships = defaultdict(dict)
        self.fund_flow_patterns = {}
        self.sector_momentum_cache = {}
        
        # 文件分类配置
        self.file_types = {
            'fund_flow': ['fund_flow_tpdog.csv', 'ths_fund_flow.csv', 'fund_flow_akshare.csv'],
            'concept_flow': ['concept_fund_flow_tpdog.csv', 'concept_fund_flow_akshare.csv', 'concept_fund_flow_'],
            'sector_flow': ['sector_fund_flow_tpdog.csv', 'sector_fund_flow_akshare.csv', 'sector_fund_flow_rank_'],
            'zt_pool': ['zt_pool.csv', 'previous_zt_pool.csv', '涨停股池_akshare_东方财富_', '涨停股池_tpdog_'],
            'zb_pool': ['炸板股池_akshare_东方财富_'],
            'news': ['news_cls.csv', 'news_em.csv', 'news_ths.csv'],
            'index': ['index_sh000001.csv', 'index_sz399006.csv'],
            'movers': ['movers_大笔买入.csv', 'movers_有大买盘.csv'],
            'big_deal': ['ths_big_deal.csv', 'big_deal_'],
            'board_changes': ['board_changes.csv'],
            'market_flow': ['market_fund_flow.csv', 'market_fund_flow_'],
            'industry_board': ['industry_board_ths.csv', 'industry_board_em.csv', 'industry_board_akshare.csv'],
            'indicator': ['indicator_创月新高.csv'],
            'individual_flow': ['individual_fund_flow_'],
            'lhb_detail': ['龙虎榜详情_当日.csv'],
            'lhb': ['lhb_jgmmtj.csv'],
            'dzjy': ['dzjy_mrmx.csv'],
            'notices': ['stock_notices.txt'],
            'sector_summary': ['sector_summary_'],
            'acceleration_signals': ['acceleration_signals_'],
            'fund_flow_rank': ['fund_flow_rank_'],
            'main_fund_flow': ['main_fund_flow_'],
            'stock_notifications': ['stock_notifications.csv'],
            'stock_signals': ['stock_signals.csv'],
            'cxg': ['创新高_前100.csv'], 'lxsz': ['连续上涨_全部.csv'], 'cxfl': ['持续放量_前100.csv'],
            'ljqs': ['量价齐升_全部.csv'], 'xstp_20ma': ['向上突破_20MA.csv'],
            'bulk_trades': [f'大宗交易每日明细_A股_{self.analysis_date.replace("-", "")}.csv'],
            'performance_report': [f'业绩报表_{self.analysis_date.replace("-", "")[0:4]}0630.csv'],
        }
        
        self.load_all_available_data()
        self._build_enhanced_analysis_cache()
    
    def load_all_available_data(self):
        """加载所有数据"""
        print(f"📊 加载 {self.analysis_date} 的数据...")
        self._load_all_day_data()
        self._load_post_market_data()
        self._load_yesterday_data()
        print("✅ 数据加载完成！")
    
    def _build_enhanced_analysis_cache(self):
        """构建增强分析缓存"""
        print("🧠 构建增强分析缓存...")
        self._build_stock_relationship_graph()
        self._analyze_fund_rotation_patterns()
        self._build_sector_momentum_cache()
        print("✅ 增强分析缓存构建完成！")
    
    def _build_stock_relationship_graph(self):
        """构建股票关系图谱"""
        target_stocks = self.get_target_list()
        
        for i, stock1 in enumerate(target_stocks):
            code1 = str(stock1.get('code', '')).zfill(6)
            for j, stock2 in enumerate(target_stocks[i+1:], i+1):
                code2 = str(stock2.get('code', '')).zfill(6)
                
                # 计算关联度
                relationship_score = self._calculate_relationship_score(stock1, stock2)
                if relationship_score > 0.3:  # 阈值
                    self.stock_relationships[code1][code2] = relationship_score
                    self.stock_relationships[code2][code1] = relationship_score
    
    def _calculate_relationship_score(self, stock1, stock2):
        """计算两只股票的关联度"""
        score = 0.0
        
        # 行业相同 +0.3
        if stock1.get('industry') == stock2.get('industry') and stock1.get('industry') != '未知':
            score += 0.3
        
        # 概念重叠 +0.2 per overlap
        concepts1 = set(self.stock_info_map.get(str(stock1.get('code', '')).zfill(6), {}).get('concepts', []))
        concepts2 = set(self.stock_info_map.get(str(stock2.get('code', '')).zfill(6), {}).get('concepts', []))
        overlap = len(concepts1 & concepts2)
        score += overlap * 0.2
        
        # 封板时间相近 +0.1
        time1 = self._parse_seal_time(stock1.get('final_seal_time'))
        time2 = self._parse_seal_time(stock2.get('final_seal_time'))
        if time1 and time2:
            time_diff = abs((time1 - time2).total_seconds()) / 60  # 分钟
            if time_diff < 30:  # 30分钟内
                score += 0.1
        
        return min(score, 1.0)
    
    def _parse_seal_time(self, time_obj):
        """解析封板时间"""
        if pd.isna(time_obj):
            return None
        try:
            time_str = str(time_obj)
            if ':' in time_str:
                return datetime.strptime(f"{self.analysis_date} {time_str}", "%Y-%m-%d %H:%M:%S")
            else:
                formatted_time = ''.join(filter(str.isdigit, time_str)).zfill(6)
                return datetime.strptime(f"{self.analysis_date} {formatted_time}", "%Y-%m-%d %H%M%S")
        except:
            return None
    
    def _analyze_fund_rotation_patterns(self):
        """分析资金轮动模式"""
        # 分析不同时间段的板块资金流向变化
        time_sectors = {}
        
        for time_key in sorted(self.all_day_data.keys()):
            if 'sector_flow' in self.all_day_data[time_key]:
                df_list = self.all_day_data[time_key]['sector_flow']
                if df_list:
                    combined_df = pd.concat(df_list).head(10)  # 取前10
                    time_sectors[time_key] = combined_df['名称'].tolist() if '名称' in combined_df.columns else []
        
        self.fund_flow_patterns = time_sectors
    
    def _build_sector_momentum_cache(self):
        """构建板块动量缓存"""
        for time_key in self.all_day_data.keys():
            if 'sector_flow' in self.all_day_data[time_key]:
                df_list = self.all_day_data[time_key]['sector_flow']
                if df_list:
                    combined_df = pd.concat(df_list)
                    self.sector_momentum_cache[time_key] = combined_df
    
    # 原有的基础方法（从原版本继承）
    def _load_all_day_data(self):
        """加载当天所有时间戳的数据文件到内存中"""
        if not os.path.exists(self.data_dir):
            print(f"  - 警告: 时间线数据目录未找到: {self.data_dir}")
            return False

        for filename in os.listdir(self.data_dir):
            time_key = self._extract_time_key(filename)
            if not time_key: continue

            file_type = self._classify_file(filename)
            if file_type == 'other': continue

            filepath = os.path.join(self.data_dir, filename)
            df = self._load_csv_safe(filepath)
            if df is not None:
                self.all_day_data[time_key][file_type].append(df)
        return bool(self.all_day_data)
    
    def _load_post_market_data(self):
        """加载盘后静态数据"""
        post_market_files = {
            'zt_pool': '涨停股池.csv', 'lhb_detail': '龙虎榜详情_当日.csv',
            'cxg': '创新高_前100.csv', 'lxsz': '连续上涨_全部.csv',
            'cxfl': '持续放量_前100.csv', 'ljqs': '量价齐升_全部.csv',
            'xstp_20ma': '向上突破_20MA.csv',
            'bulk_trades': f'大宗交易每日明细_A股_{self.analysis_date.replace("-", "")}.csv',
            'performance_report': f'业绩报表_{self.analysis_date.replace("-", "")[0:4]}0630.csv',
        }
        
        if not os.path.exists(self.post_market_data_dir):
            print(f"  - 警告: 盘后数据目录未找到: {self.post_market_data_dir}")
            return

        for key, filename in post_market_files.items():
            filepath = os.path.join(self.post_market_data_dir, filename)
            if os.path.exists(filepath):
                df = self._load_csv_safe(filepath)
                if df is not None:
                    self.post_market_data[key] = df
            elif '业绩' in key:
                for quarter in ['0331', '0930', '1231']:
                    alt_filename = filename.replace('0630', quarter)
                    alt_filepath = os.path.join(self.post_market_data_dir, alt_filename)
                    if os.path.exists(alt_filepath):
                        df = self._load_csv_safe(alt_filepath)
                        if df is not None: 
                            self.post_market_data[key] = df
                        break
    
    def _load_yesterday_data(self):
        """加载前一交易日的所有相关数据"""
        # 1. 加载昨日的时间线数据
        if os.path.exists(self.yesterday_data_dir):
            for filename in os.listdir(self.yesterday_data_dir):
                file_type = self._classify_file(filename)
                if file_type == 'other': continue
                filepath = os.path.join(self.yesterday_data_dir, filename)
                df = self._load_csv_safe(filepath)
                if df is not None:
                    self.yesterday_data['intraday'][file_type].append(df)

        # 2. 加载昨日的盘后数据
        post_market_files = {
            'lhb_detail': '龙虎榜详情_当日.csv', 'cxg': '创新高_前100.csv', 'lxsz': '连续上涨_全部.csv',
            'bulk_trades': f'大宗交易每日明细_A股_{os.path.basename(self.yesterday_post_market_data_dir)}.csv'
        }
        if os.path.exists(self.yesterday_post_market_data_dir):
            for key, filename in post_market_files.items():
                filepath = os.path.join(self.yesterday_post_market_data_dir, filename)
                if os.path.exists(filepath):
                    df = self._load_csv_safe(filepath)
                    if df is not None:
                        self.yesterday_data['post_market'][key] = df
    
    def get_target_list(self):
        """读取收盘后的涨停股池文件，确定要分析的目标列表"""
        df_eod_zt = None
        # 优先从盘后数据中找
        if 'zt_pool' in self.post_market_data:
            df_eod_zt = self.post_market_data['zt_pool']

        # 如果盘后没有，再从盘中数据倒序找
        if df_eod_zt is None:
            for time_key in sorted(self.all_day_data.keys(), reverse=True):
                if 'zt_pool' in self.all_day_data[time_key]:
                    valid_dfs = [df for df in self.all_day_data[time_key]['zt_pool'] if '涨停原因' in df.columns]
                    if valid_dfs:
                        df_eod_zt = valid_dfs[0]
                        break

        if df_eod_zt is None or df_eod_zt.empty:
            return []

        # 标准化列名以兼容不同来源
        df_eod_zt.rename(columns={
            "代码": "code", "名称": "name", "涨跌幅": "change_pct",
            "连板数": "consecutive_boards", "炸板次数": "broken_times",
            "最后封板时间": "final_seal_time", "所属行业": "industry"
        }, inplace=True, errors='ignore')

        # 构建股票信息映射
        self._build_stock_info_map(df_eod_zt.to_dict('records'))

        return df_eod_zt.to_dict('records')
    
    def _build_stock_info_map(self, target_stocks):
        """构建股票信息映射"""
        # 从概念板块文件构建
        for time_key in sorted(self.all_day_data.keys()):
            if 'concept_flow' in self.all_day_data[time_key]:
                for df in self.all_day_data[time_key]['concept_flow']:
                    if '相关个股' in df.columns and '概念名称' in df.columns:
                        for _, row in df.iterrows():
                            concept_name = row['概念名称']
                            stocks = str(row['相关个股']).split(',')
                            for stock_str in stocks[:3]:
                                stock_code = stock_str.split(' ')[0]
                                if stock_code.isdigit():
                                    if stock_code not in self.stock_info_map:
                                        self.stock_info_map[stock_code] = {'industry': '未知', 'concepts': set()}
                                    self.stock_info_map[stock_code]['concepts'].add(concept_name)

        # 从最终涨停池补充行业信息
        for stock in target_stocks:
            code = str(stock.get('code', '')).zfill(6)
            industry = stock.get('industry', '未知')
            if code in self.stock_info_map:
                if self.stock_info_map[code]['industry'] == '未知':
                    self.stock_info_map[code]['industry'] = industry
            else:
                self.stock_info_map[code] = {'industry': industry, 'concepts': set()}
    
    # 辅助方法
    def _extract_time_key(self, filename):
        match = re.search(r'(\d{2})-(\d{2})_', filename) or \
                re.search(r'_\d{8}_(\d{2})(\d{2})\d{2}', filename) or \
                re.search(r'_(\d{2})(\d{2})\d{2}\.csv$', filename)
        return f"{match.group(1)}:{match.group(2)}" if match else None

    def _classify_file(self, filename):
        all_patterns = []
        for file_type, patterns in self.file_types.items():
            for pattern in patterns:
                all_patterns.append((pattern, file_type))
        all_patterns.sort(key=lambda x: len(x[0]), reverse=True)
        for pattern, file_type in all_patterns:
            if pattern in filename: return file_type
        return 'other'

    def _load_csv_safe(self, filepath):
        if not os.path.exists(filepath): return None
        try:
            return pd.read_csv(filepath, encoding='utf-8', on_bad_lines='skip')
        except:
            try:
                return pd.read_csv(filepath, encoding='gbk', on_bad_lines='skip')
            except:
                return None

# 初始化增强版工具箱实例
enhanced_toolbox = EnhancedZtToolbox(
    analysis_date=ANALYSIS_DATE,
    data_dir=FUND_DATA_DIR,
    post_market_data_dir=POST_MARKET_DATA_DIR
)

# --- 6个高级分析工具 ---

@tool
def analyze_sector_momentum(sector_name: str, time_window: str = "全天") -> str:
    """
    分析指定板块的资金动量变化趋势。
    AI可以通过此工具主动发现板块轮动规律和异常资金流入时点。
    
    Args:
        sector_name: 板块名称
        time_window: 时间窗口，如"09:30-10:30"或"全天"
    """
    global enhanced_toolbox
    
    momentum_data = []
    
    if time_window == "全天":
        time_keys = sorted(enhanced_toolbox.all_day_data.keys())
    else:
        # 解析时间窗口
        try:
            start_time, end_time = time_window.split('-')
            time_keys = [tk for tk in sorted(enhanced_toolbox.all_day_data.keys()) 
                        if start_time <= tk <= end_time]
        except:
            time_keys = sorted(enhanced_toolbox.all_day_data.keys())
    
    for time_key in time_keys:
        if time_key in enhanced_toolbox.sector_momentum_cache:
            df = enhanced_toolbox.sector_momentum_cache[time_key]
            if '名称' in df.columns:
                sector_row = df[df['名称'] == sector_name]
                if not sector_row.empty:
                    rank = sector_row.index[0] + 1
                    net_inflow = sector_row.iloc[0].get('净流入', 0) if '净流入' in sector_row.columns else 0
                    momentum_data.append(f"{time_key}: 排名第{rank}名, 净流入{net_inflow/1e8:.2f}亿")
    
    if not momentum_data:
        return f"板块'{sector_name}'在指定时间窗口内未发现明显的资金动量变化。"
    
    # 分析趋势
    analysis = f"板块'{sector_name}'的资金动量分析:\n"
    analysis += "\n".join(momentum_data[:10])  # 显示前10个时间点
    
    # 添加趋势判断
    if len(momentum_data) >= 3:
        analysis += f"\n\n📈 动量趋势: 该板块在{len(momentum_data)}个时间点出现异动，显示出"
        if "排名第1名" in momentum_data[0] or "排名第2名" in momentum_data[0]:
            analysis += "强势资金集中流入的特征。"
        else:
            analysis += "渐进式资金关注的特征。"
    
    return analysis

@tool 
def discover_hidden_connections(stock_code: str) -> str:
    """
    发现股票与其他涨停股之间的隐藏关联。
    分析相同概念、上下游产业链、资金相关性等多维度连接。
    
    Args:
        stock_code: 股票代码
    """
    global enhanced_toolbox
    
    stock_code = stock_code.zfill(6)
    connections = []
    
    # 1. 直接关联关系
    if stock_code in enhanced_toolbox.stock_relationships:
        related_stocks = enhanced_toolbox.stock_relationships[stock_code]
        for related_code, score in sorted(related_stocks.items(), key=lambda x: x[1], reverse=True)[:5]:
            # 获取股票名称
            target_stocks = enhanced_toolbox.get_target_list()
            related_name = "未知"
            for stock in target_stocks:
                if str(stock.get('code', '')).zfill(6) == related_code:
                    related_name = stock.get('name', '未知')
                    break
            
            connections.append(f"与 {related_name}({related_code}) 关联度{score:.2f}")
    
    # 2. 概念重叠分析  
    stock_info = enhanced_toolbox.stock_info_map.get(stock_code, {})
    stock_concepts = stock_info.get('concepts', set())
    
    if stock_concepts:
        concept_connections = []
        target_stocks = enhanced_toolbox.get_target_list()
        
        for stock in target_stocks:
            other_code = str(stock.get('code', '')).zfill(6)
            if other_code == stock_code:
                continue
                
            other_info = enhanced_toolbox.stock_info_map.get(other_code, {})
            other_concepts = other_info.get('concepts', set())
            
            overlap = stock_concepts & other_concepts
            if overlap:
                concept_connections.append(f"{stock.get('name')}({other_code}): 共同概念{list(overlap)}")
        
        if concept_connections:
            connections.extend(concept_connections[:3])  # 最多显示3个
    
    # 3. 行业关联
    stock_industry = stock_info.get('industry', '未知')
    if stock_industry != '未知':
        industry_peers = []
        target_stocks = enhanced_toolbox.get_target_list()
        
        for stock in target_stocks:
            other_code = str(stock.get('code', '')).zfill(6)
            if other_code == stock_code:
                continue
                
            if stock.get('industry') == stock_industry:
                industry_peers.append(f"{stock.get('name')}({other_code})")
        
        if industry_peers:
            connections.append(f"同行业({stock_industry})伙伴: {', '.join(industry_peers[:3])}")
    
    if not connections:
        return f"股票{stock_code}与其他涨停股暂未发现明显的隐藏关联。可能属于独立驱动的个股。"
    
    result = f"🔍 股票{stock_code}的隐藏关联网络:\n"
    result += "\n".join([f"• {conn}" for conn in connections])
    
    return result

@tool
def trace_fund_flow_patterns(time_start: str, time_end: str) -> str:
    """
    追踪指定时间段内的主力资金流向模式。
    识别资金从哪些板块流出，流入哪些板块，发现市场轮动逻辑。
    
    Args:
        time_start: 开始时间，如"09:30"
        time_end: 结束时间，如"10:30"
    """
    global enhanced_toolbox
    
    # 获取时间范围内的数据
    time_keys = [tk for tk in sorted(enhanced_toolbox.all_day_data.keys()) 
                if time_start <= tk <= time_end]
    
    if not time_keys:
        return f"在时间段{time_start}-{time_end}内未找到有效数据。"
    
    # 分析板块排名变化
    sector_rankings = {}
    
    for time_key in time_keys:
        if time_key in enhanced_toolbox.sector_momentum_cache:
            df = enhanced_toolbox.sector_momentum_cache[time_key]
            if '名称' in df.columns:
                # 取前20个板块
                top_sectors = df.head(20)
                for idx, row in top_sectors.iterrows():
                    sector_name = row['名称']
                    rank = idx + 1
                    
                    if sector_name not in sector_rankings:
                        sector_rankings[sector_name] = []
                    sector_rankings[sector_name].append((time_key, rank))
    
    # 分析资金流向模式
    flow_patterns = []
    
    # 1. 持续强势板块（排名一直靠前）
    strong_sectors = []
    for sector, rankings in sector_rankings.items():
        if len(rankings) >= len(time_keys) * 0.7:  # 出现在70%以上的时间点
            avg_rank = sum([rank for _, rank in rankings]) / len(rankings)
            if avg_rank <= 10:
                strong_sectors.append((sector, avg_rank))
    
    if strong_sectors:
        strong_sectors.sort(key=lambda x: x[1])
        flow_patterns.append(f"💪 持续强势板块: {', '.join([f'{s}(平均第{r:.1f}名)' for s, r in strong_sectors[:3]])}")
    
    # 2. 新兴热点板块（排名快速上升）
    emerging_sectors = []
    for sector, rankings in sector_rankings.items():
        if len(rankings) >= 2:
            first_rank = rankings[0][1]
            last_rank = rankings[-1][1]
            if first_rank > 20 and last_rank <= 10:
                emerging_sectors.append((sector, first_rank - last_rank))
    
    if emerging_sectors:
        emerging_sectors.sort(key=lambda x: x[1], reverse=True)
        flow_patterns.append(f"🚀 新兴热点板块: {', '.join([f'{s}(上升{r}位)' for s, r in emerging_sectors[:3]])}")
    
    # 3. 资金轮动特征
    if len(time_keys) >= 3:
        early_leaders = []
        late_leaders = []
        
        # 前1/3时间的领先板块
        early_time_keys = time_keys[:len(time_keys)//3]
        for time_key in early_time_keys:
            if time_key in enhanced_toolbox.fund_flow_patterns:
                early_leaders.extend(enhanced_toolbox.fund_flow_patterns[time_key][:3])
        
        # 后1/3时间的领先板块  
        late_time_keys = time_keys[-len(time_keys)//3:]
        for time_key in late_time_keys:
            if time_key in enhanced_toolbox.fund_flow_patterns:
                late_leaders.extend(enhanced_toolbox.fund_flow_patterns[time_key][:3])
        
        # 计算轮动
        early_set = set(early_leaders)
        late_set = set(late_leaders)
        
        rotated_out = early_set - late_set
        rotated_in = late_set - early_set
        
        if rotated_out or rotated_in:
            rotation_info = []
            if rotated_out:
                rotation_info.append(f"流出: {', '.join(list(rotated_out)[:3])}")
            if rotated_in:
                rotation_info.append(f"流入: {', '.join(list(rotated_in)[:3])}")
            flow_patterns.append(f"🔄 资金轮动: {' | '.join(rotation_info)}")
    
    if not flow_patterns:
        return f"在时间段{time_start}-{time_end}内未发现明显的资金流向模式。"
    
    result = f"💰 时间段{time_start}-{time_end}的资金流向模式:\n"
    result += "\n".join([f"• {pattern}" for pattern in flow_patterns])
    
    return result

@tool
def identify_anomaly_signals(stock_code: str) -> str:
    """
    识别股票的异常信号组合。
    检测价量配合、技术突破、消息催化等多重信号的时间序列异常。
    
    Args:
        stock_code: 股票代码
    """
    global enhanced_toolbox
    
    stock_code = stock_code.zfill(6)
    anomaly_signals = []
    
    # 获取股票信息
    target_stocks = enhanced_toolbox.get_target_list()
    stock_name = "未知"
    for stock in target_stocks:
        if str(stock.get('code', '')).zfill(6) == stock_code:
            stock_name = stock.get('name', '未知')
            break
    
    # 1. 资金流异常检测
    fund_anomalies = []
    best_rank = 101
    
    for time_key in sorted(enhanced_toolbox.all_day_data.keys()):
        data_at_time = enhanced_toolbox.all_day_data.get(time_key, {})
        
        if 'individual_flow' in data_at_time:
            for df in data_at_time['individual_flow']:
                if '代码' in df.columns:
                    df['代码'] = df['代码'].astype(str).str.zfill(6)
                    stock_info = df[df['代码'] == stock_code]
                    if not stock_info.empty:
                        rank = stock_info.index[0] + 1
                        if rank < best_rank:
                            best_rank = rank
                            # 检测是否为跳跃式排名提升
                            if best_rank <= 10 and time_key <= "10:00":
                                fund_anomalies.append(f"{time_key}: 资金排名急升至第{rank}名 (异常强势)")
                            elif best_rank <= 20:
                                fund_anomalies.append(f"{time_key}: 资金排名升至第{rank}名")
    
    if fund_anomalies:
        anomaly_signals.extend(fund_anomalies[:3])
    
    # 2. 大买盘异常集中
    big_order_times = []
    for time_key in sorted(enhanced_toolbox.all_day_data.keys()):
        data_at_time = enhanced_toolbox.all_day_data.get(time_key, {})
        
        if 'movers' in data_at_time:
            for df in data_at_time['movers']:
                if '名称' in df.columns and stock_name in df['名称'].values:
                    big_order_times.append(time_key)
                    break
    
    if big_order_times:
        # 检测是否在短时间内集中出现
        if len(big_order_times) >= 3:
            time_span = []
            for i in range(len(big_order_times)-1):
                t1 = datetime.strptime(big_order_times[i], "%H:%M")
                t2 = datetime.strptime(big_order_times[i+1], "%H:%M")
                span = (t2 - t1).total_seconds() / 60
                time_span.append(span)
            
            avg_span = sum(time_span) / len(time_span)
            if avg_span < 15:  # 平均间隔小于15分钟
                anomaly_signals.append(f"大买盘信号异常集中: {len(big_order_times)}次信号，平均间隔{avg_span:.1f}分钟")
            else:
                anomaly_signals.append(f"大买盘信号持续: {len(big_order_times)}次信号分布在全天")
    
    # 3. 板块同步异常
    stock_info = enhanced_toolbox.stock_info_map.get(stock_code, {})
    stock_industry = stock_info.get('industry', '未知')
    
    if stock_industry != '未知':
        # 检查个股与板块的同步性
        individual_signals = 0
        sector_signals = 0
        
        for time_key in sorted(enhanced_toolbox.all_day_data.keys())[:10]:  # 检查前10个时间点
            # 个股是否有资金流入
            if 'individual_flow' in enhanced_toolbox.all_day_data.get(time_key, {}):
                for df in enhanced_toolbox.all_day_data[time_key]['individual_flow']:
                    if '代码' in df.columns:
                        df['代码'] = df['代码'].astype(str).str.zfill(6)
                        if stock_code in df['代码'].values:
                            individual_signals += 1
                            break
            
            # 板块是否排名靠前
            if time_key in enhanced_toolbox.sector_momentum_cache:
                df = enhanced_toolbox.sector_momentum_cache[time_key]
                if '名称' in df.columns:
                    sector_row = df[df['名称'] == stock_industry]
                    if not sector_row.empty and sector_row.index[0] < 20:  # 前20名
                        sector_signals += 1
        
        # 异常检测：个股强但板块弱，或个股弱但板块强
        if individual_signals >= 3 and sector_signals <= 1:
            anomaly_signals.append(f"个股独立强势: 个股有{individual_signals}次资金信号，但板块({stock_industry})表现平淡")
        elif individual_signals <= 1 and sector_signals >= 3:
            anomaly_signals.append(f"板块带动效应: 板块({stock_industry})持续强势，个股后知后觉")
    
    # 4. 技术突破异常
    tech_signals = []
    tech_map = {'cxg': '创月新高', 'lxsz': '连续上涨', 'cxfl': '持续放量', 'ljqs': '量价齐升', 'xstp_20ma': '向上突破20日线'}
    
    for key, signal_name in tech_map.items():
        if key in enhanced_toolbox.post_market_data:
            df = enhanced_toolbox.post_market_data[key]
            if '代码' in df.columns and stock_code in df['代码'].astype(str).str.zfill(6).values:
                tech_signals.append(signal_name)
    
    if len(tech_signals) >= 2:
        anomaly_signals.append(f"技术信号共振: 同时触发{len(tech_signals)}个技术信号 ({', '.join(tech_signals)})")
    
    if not anomaly_signals:
        return f"股票{stock_name}({stock_code})未检测到明显的异常信号组合，属于常规涨停模式。"
    
    result = f"⚠️ 股票{stock_name}({stock_code})的异常信号检测:\n"
    result += "\n".join([f"• {signal}" for signal in anomaly_signals])
    
    return result

@tool
def detect_lead_lag_relationships(stock_code: str) -> str:
    """
    检测股票的领涨-跟涨关系。
    分析该股票是板块龙头还是跟风股，以及其对其他股票的影响力。
    
    Args:
        stock_code: 股票代码
    """
    global enhanced_toolbox
    
    stock_code = stock_code.zfill(6)
    
    # 获取股票信息
    target_stocks = enhanced_toolbox.get_target_list()
    current_stock = None
    stock_name = "未知"
    
    for stock in target_stocks:
        if str(stock.get('code', '')).zfill(6) == stock_code:
            current_stock = stock
            stock_name = stock.get('name', '未知')
            break
    
    if not current_stock:
        return f"未找到股票{stock_code}的信息。"
    
    # 获取封板时间
    seal_time = enhanced_toolbox._parse_seal_time(current_stock.get('final_seal_time'))
    if not seal_time:
        return f"股票{stock_name}({stock_code})的封板时间不明确，无法分析领涨-跟涨关系。"
    
    stock_industry = current_stock.get('industry', '未知')
    relationships = []
    
    # 1. 分析同板块内的时间序列
    if stock_industry != '未知':
        same_industry_stocks = []
        for stock in target_stocks:
            if stock.get('industry') == stock_industry and str(stock.get('code', '')).zfill(6) != stock_code:
                other_seal_time = enhanced_toolbox._parse_seal_time(stock.get('final_seal_time'))
                if other_seal_time:
                    same_industry_stocks.append({
                        'name': stock.get('name'),
                        'code': str(stock.get('code', '')).zfill(6),
                        'seal_time': other_seal_time
                    })
        
        if same_industry_stocks:
            # 按封板时间排序
            same_industry_stocks.sort(key=lambda x: x['seal_time'])
            current_rank = None
            
            # 找到当前股票在时间序列中的位置
            all_stocks = same_industry_stocks + [{
                'name': stock_name,
                'code': stock_code,
                'seal_time': seal_time
            }]
            all_stocks.sort(key=lambda x: x['seal_time'])
            
            for i, s in enumerate(all_stocks):
                if s['code'] == stock_code:
                    current_rank = i + 1
                    break
            
            if current_rank == 1:
                followers = [s['name'] for s in all_stocks[1:]]
                relationships.append(f"🏆 板块龙头地位: 在{stock_industry}板块中率先封板，带动后续{len(followers)}只个股")
                if followers:
                    relationships.append(f"  └─ 跟随者: {', '.join(followers[:3])}")
            else:
                leaders = [s['name'] for s in all_stocks[:current_rank-1]]
                relationships.append(f"📈 跟风助攻地位: 在{stock_industry}板块中第{current_rank}个封板，跟随{len(leaders)}只龙头")
                if leaders:
                    relationships.append(f"  └─ 领先者: {', '.join(leaders)}")
    
    # 2. 分析概念关联的影响力
    stock_info = enhanced_toolbox.stock_info_map.get(stock_code, {})
    stock_concepts = stock_info.get('concepts', set())
    
    if stock_concepts:
        concept_influence = {}
        
        for concept in list(stock_concepts)[:3]:  # 分析前3个概念
            concept_stocks = []
            for stock in target_stocks:
                other_code = str(stock.get('code', '')).zfill(6)
                if other_code == stock_code:
                    continue
                
                other_info = enhanced_toolbox.stock_info_map.get(other_code, {})
                other_concepts = other_info.get('concepts', set())
                
                if concept in other_concepts:
                    other_seal_time = enhanced_toolbox._parse_seal_time(stock.get('final_seal_time'))
                    if other_seal_time:
                        time_diff = (other_seal_time - seal_time).total_seconds() / 60  # 分钟
                        concept_stocks.append({
                            'name': stock.get('name'),
                            'time_diff': time_diff
                        })
            
            if concept_stocks:
                # 统计时间关系
                led_count = len([s for s in concept_stocks if s['time_diff'] > 0])  # 被带动的
                follow_count = len([s for s in concept_stocks if s['time_diff'] < 0])  # 跟随的
                
                if led_count > 0:
                    led_names = [s['name'] for s in concept_stocks if s['time_diff'] > 0][:2]
                    concept_influence[concept] = f"带动{led_count}只 ({', '.join(led_names)})"
                elif follow_count > 0:
                    follow_names = [s['name'] for s in concept_stocks if s['time_diff'] < 0][:2]
                    concept_influence[concept] = f"跟随{follow_count}只 ({', '.join(follow_names)})"
        
        if concept_influence:
            relationships.append("🎯 概念影响力分析:")
            for concept, influence in concept_influence.items():
                relationships.append(f"  └─ {concept}: {influence}")
    
    # 3. 资金流向的领先-滞后分析
    if seal_time.hour <= 10:  # 早盘封板
        # 检查是否带动了板块资金流入
        seal_time_str = seal_time.strftime("%H:%M")
        
        # 寻找封板后30分钟内的板块排名变化
        end_time = (seal_time + timedelta(minutes=30)).strftime("%H:%M")
        
        sector_improvement = False
        if stock_industry != '未知':
            for time_key in sorted(enhanced_toolbox.all_day_data.keys()):
                if seal_time_str <= time_key <= end_time:
                    if time_key in enhanced_toolbox.sector_momentum_cache:
                        df = enhanced_toolbox.sector_momentum_cache[time_key]
                        if '名称' in df.columns:
                            sector_row = df[df['名称'] == stock_industry]
                            if not sector_row.empty and sector_row.index[0] <= 10:  # 进入前10
                                sector_improvement = True
                                break
        
        if sector_improvement:
            relationships.append(f"💪 资金带动效应: 早盘封板后30分钟内推动{stock_industry}板块排名进入前10")
        else:
            relationships.append("🔍 独立行情: 个股强势但未能显著带动板块资金流入")
    
    if not relationships:
        return f"股票{stock_name}({stock_code})的领涨-跟涨关系特征不明显，可能属于独立驱动的个股。"
    
    result = f"🔗 股票{stock_name}({stock_code})的领涨-跟涨关系分析:\n"
    result += "\n".join(relationships)
    
    return result

@tool
def generate_insight_driven_report_section(discoveries: str, section_type: str) -> str:
    """
    基于AI探索发现的洞察，智能生成报告章节。
    根据发现的独特特征动态调整报告重点和结构。
    
    Args:
        discoveries: AI探索发现的关键洞察（JSON格式字符串）
        section_type: 报告章节类型（market_overview/sector_analysis/risk_assessment）
    """
    try:
        # 尝试解析JSON格式的发现
        import json
        discoveries_dict = json.loads(discoveries)
    except:
        # 如果不是JSON，直接使用文本
        discoveries_dict = {"raw_text": discoveries}
    
    if section_type == "market_overview":
        return f"""
## 🎯 AI智能市场洞察

基于深度数据挖掘，本日市场呈现以下核心特征：

{discoveries}

**AI洞察总结**: 通过多维度关联分析，市场展现出独特的结构性机会。建议重点关注具备多重确认信号的标的。
"""
    
    elif section_type == "sector_analysis":
        return f"""
## 🔍 板块深度解构

AI通过资金流向追踪和关联网络分析，发现：

{discoveries}

**策略建议**: 重点关注资金持续流入且具备技术共振的板块龙头。
"""
    
    elif section_type == "risk_assessment":
        return f"""
## ⚠️ 智能风险评估

基于异常信号检测和领涨-跟涨分析：

{discoveries}

**风险提示**: 关注信号异常集中但基本面支撑不足的标的，存在回调风险。
"""
    
    else:
        return f"## 📊 数据洞察\n\n{discoveries}"


# --- 增强版Agent系统 ---

def run_enhanced_detective_agent():
    """
    运行增强版涨停侦探Agent - 具备真正的AI探索能力
    """
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    # 初始化LLM
    logging.info("🤖 正在初始化增强版AI侦探...")
    current_api_key = get_current_api_key()
    
    llm = ChatOpenAI(
        model=SILICONFLOW_MODEL_NAME,
        temperature=0.3,  # 提高一些创造性
        openai_api_key=current_api_key,
        openai_api_base="https://api.siliconflow.cn/v1",
    )

    # 所有工具
    tools = [
        analyze_sector_momentum,
        discover_hidden_connections, 
        trace_fund_flow_patterns,
        identify_anomaly_signals,
        detect_lead_lag_relationships,
        generate_insight_driven_report_section
    ]

    agent = initialize_agent(
        tools, llm, agent=AgentType.STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION, verbose=True
    )

    # 增强版Agent指令 - 强化探索性思维
    enhanced_agent_prompt = """
你是一位世界顶级的量化分析师和市场侦探，具备深度思考和探索能力。你的使命是通过多层次分析，揭示涨停股背后的深层逻辑。

## 🧠 你的分析思维框架：

### 第一层：现象观察 (Surface Level)
1. 首先收集股票的基本涨停信息（代码、名称、涨幅、板块等）

### 第二层：关联挖掘 (Connection Level) 
2. **主动使用 discover_hidden_connections** 工具，寻找与其他涨停股的隐藏关联
   - 不要满足于表面的行业分类，要挖掘深层的概念重叠和资金关联
   - 寻找"为什么这些股票会在同一天涨停"的内在逻辑

### 第三层：动量分析 (Momentum Level)
3. **使用 analyze_sector_momentum** 分析板块资金动量变化
   - 重点关注资金流入的时间节点和强度变化
   - 识别是板块先动还是个股先动
   
4. **使用 trace_fund_flow_patterns** 追踪资金轮动模式
   - 发现资金从哪里来，到哪里去
   - 识别市场的主要驱动力量

### 第四层：异常识别 (Anomaly Level)
5. **使用 identify_anomaly_signals** 检测异常信号组合
   - 寻找不同寻常的信号模式
   - 识别可能的催化剂和风险点

### 第五层：影响力评估 (Leadership Level)  
6. **使用 detect_lead_lag_relationships** 分析领导-跟随关系
   - 判断该股票是龙头还是跟风
   - 评估其对市场的影响力和持续性

### 第六层：洞察综合 (Insight Synthesis)
7. 基于前面的发现，形成独特的投资洞察和逻辑链条
8. **使用 generate_insight_driven_report_section** 生成智能报告

## 🎯 你的探索原则：

1. **主动性**: 不要被动等待指令，要主动选择最合适的工具组合
2. **关联性**: 始终寻找事物之间的连接和模式
3. **异常性**: 特别关注不符合常规的现象和信号
4. **逻辑性**: 每个结论都要有数据支撑和逻辑链条
5. **前瞻性**: 不仅分析现在，还要预测未来的发展趋势

## 📝 报告要求：

你的最终报告应该包含：
- **核心发现**: 这只股票涨停的最核心原因
- **关联网络**: 与其他股票/板块的关联关系
- **异常信号**: 发现的异常模式和风险点  
- **影响力评估**: 龙头地位和持续性预测
- **投资启示**: 基于分析的实战建议

记住：你不是简单的数据查询工具，而是具备洞察力的分析师。要通过数据发现故事，通过关联找到逻辑，通过异常识别机会。

现在开始你的探索之旅！
"""

    target_stocks = enhanced_toolbox.get_target_list()
    if not target_stocks:
        print("❌ 未找到涨停股数据")
        return

    print(f"\n🕵️‍♂️ 增强版AI涨停侦探开始工作，深度分析 {len(target_stocks)} 只股票...")

    # 智能采样：优先分析连板股和热门板块股票
    priority_stocks = []
    regular_stocks = []
    
    for stock in target_stocks:
        # 连板股或热门板块优先
        if (stock.get('consecutive_boards', 1) > 1 or 
            stock.get('industry') in ['软件开发', '半导体', '文化传媒', '消费电子']):
            priority_stocks.append(stock)
        else:
            regular_stocks.append(stock)
    
    # 分析优先股票（全部）+ 常规股票（采样）
    analysis_stocks = priority_stocks + regular_stocks[:max(5, 10-len(priority_stocks))]
    
    all_reports = []
    
    for i, stock in enumerate(analysis_stocks, 1):
        code = str(stock.get('code', '')).zfill(6)
        name = stock.get('name', '未知名称')
        industry = stock.get('industry', '未知')
        consecutive_boards = stock.get('consecutive_boards', 1)

        print(f"\n\n===== 🔍 深度分析 ({i}/{len(analysis_stocks)}): {name} ({code}) =====")
        print(f"基本信息: {consecutive_boards}连板, 所属行业: {industry}")

        # 动态生成探索性查询
        exploration_query = f"""
我需要对涨停股 {name}({code}) 进行深度探索性分析。

股票基本信息：
- 名称: {name}
- 代码: {code}  
- 连板数: {consecutive_boards}
- 所属行业: {industry}

请按照你的六层分析框架，深度探索这只股票：

1. 首先分析它的关联网络 - 与哪些股票存在隐藏关联？
2. 然后分析板块动量 - {industry}板块的资金流向模式如何？
3. 追踪资金轮动 - 从早盘到现在，资金是如何流动的？
4. 识别异常信号 - 这只股票有哪些异常的信号组合？
5. 评估影响力 - 它是龙头还是跟风？对其他股票有什么影响？
6. 生成深度洞察 - 基于发现的数据，这只股票涨停的核心逻辑是什么？

请主动选择最合适的工具组合，进行探索性分析。我期待你发现独特的洞察！
"""

        try:
            # 让Agent自主探索
            full_query = enhanced_agent_prompt + "\n\n" + exploration_query
            result = agent.run(full_query)
            
            # 保存单个股票的分析报告
            stock_filename = os.path.join(OUTPUT_DIR, f"enhanced_analysis_{code}_{name}.md")
            with open(stock_filename, 'w', encoding='utf-8') as f:
                f.write(f"# {name}({code}) - AI深度探索分析报告\n\n")
                f.write(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"**连板数**: {consecutive_boards}连板\n")
                f.write(f"**所属行业**: {industry}\n\n")
                f.write("---\n\n")
                f.write(result)
            
            all_reports.append({
                'stock': stock,
                'analysis': result
            })
            
            print(f"✅ 完成分析: {name}")
            
        except Exception as e:
            error_msg = f"❌ 分析 {name} 时发生错误: {e}"
            print(error_msg)
            all_reports.append({
                'stock': stock,
                'analysis': error_msg
            })

    # 生成综合报告
    print(f"\n📊 正在生成综合AI洞察报告...")
    
    comprehensive_report = f"""# {ANALYSIS_DATE} 增强版AI涨停侦探综合报告

> 🤖 本报告由增强版LangChain AI侦探生成，具备深度探索和关联分析能力

## 📈 分析概览

本次分析了 **{len(analysis_stocks)}** 只重点涨停股票，其中：
- 连板股: {len(priority_stocks)} 只
- 首板股: {len(analysis_stocks) - len(priority_stocks)} 只
- 总涨停股数: {len(target_stocks)} 只

## 🧠 AI核心发现

通过多维度关联分析和异常信号检测，AI发现了以下关键模式：

"""

    # 添加每只股票的分析结果
    for i, report in enumerate(all_reports, 1):
        stock = report['stock']
        analysis = report['analysis']
        name = stock.get('name', '未知')
        code = str(stock.get('code', '')).zfill(6)
        
        comprehensive_report += f"""
## {i}. {name} ({code}) - AI深度洞察

{analysis}

---

"""

    # 保存综合报告
    comprehensive_filename = os.path.join(OUTPUT_DIR, f"enhanced_comprehensive_report_{ANALYSIS_DATE}.md")
    with open(comprehensive_filename, 'w', encoding='utf-8') as f:
        f.write(comprehensive_report)

    print(f"\n🎉 增强版AI分析完成！")
    print(f"📁 综合报告: {comprehensive_filename}")
    print(f"📁 单股报告目录: {OUTPUT_DIR}")
    print(f"🔢 共分析 {len(analysis_stocks)} 只重点股票，总涨停股数 {len(target_stocks)} 只")


if __name__ == "__main__":
    run_enhanced_detective_agent()