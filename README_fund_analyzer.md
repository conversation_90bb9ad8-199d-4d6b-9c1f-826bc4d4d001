# 基金数据时间序列分析器

这个Python脚本可以按时间段分析您的fund_data文件夹中的股市数据，并生成详细的日报告。

## 功能特点

### 1. 全面的数据分析
- **资金流向分析**：分析个股、概念板块、行业板块的资金流向
- **涨停池监控**：跟踪涨停股票的变化情况
- **新闻事件**：整合多平台新闻信息
- **异动股票**：监控大笔买入、有大买盘等异动情况
- **指数走势**：跟踪上证指数、深证成指等主要指数

### 2. 时间序列对比
- 自动识别文件中的时间信息（格式：HH-MM）
- 对比相邻时间段的数据变化
- 识别新增涨停、失去涨停的股票
- 分析资金流向的变化趋势

### 3. 智能报告生成
- 按时间段生成详细分析
- 全天数据汇总和统计
- 活跃时间段识别
- 投资建议和关注重点

## 支持的文件类型

脚本会自动识别以下类型的数据文件：

| 文件类型 | 文件名模式 | 说明 |
|---------|-----------|------|
| 资金流向 | `*fund_flow_tpdog.csv`, `*ths_fund_flow.csv` | 个股资金流向数据 |
| 概念板块 | `*concept_fund_flow_tpdog.csv` | 概念板块资金流向 |
| 行业板块 | `*sector_fund_flow_tpdog.csv` | 行业板块资金流向 |
| 涨停池 | `*zt_pool.csv` | 涨停股票池 |
| 新闻数据 | `*news_cls.csv`, `*news_em.csv`, `*news_ths.csv` | 各平台新闻 |
| 指数数据 | `*index_sh000001.csv`, `*index_sz399006.csv` | 主要指数数据 |
| 异动股票 | `*movers_*.csv` | 各类异动股票 |
| 大单交易 | `*ths_big_deal.csv` | 大单交易数据 |
| 板块变化 | `*board_changes.csv` | 板块变化情况 |

## 使用方法

### 1. 基本使用

```python
# 直接运行脚本
python fund_data_analyzer.py
```

### 2. 修改分析日期

在脚本开头修改配置参数：

```python
# 配置参数
ANALYSIS_DATE = "2025-07-14"  # 修改为您要分析的日期
FUND_DATA_DIR = "../fund_data"  # 数据文件夹路径
OUTPUT_DIR = "../reports"  # 报告输出文件夹
```

### 3. 自定义分析

```python
from fund_data_analyzer import FundDataAnalyzer

# 创建分析器实例
analyzer = FundDataAnalyzer("2025-07-14", "fund_data")

# 生成报告
report = analyzer.generate_daily_report()

# 保存报告
analyzer.save_report(report)
```

## 报告示例

生成的报告包含以下部分：

### 数据概览
```
================================================================================
                    2025-07-14 股市数据分析报告
================================================================================

📊 数据概览:
- 分析日期: 2025-07-14
- 数据时间段: 09:30 - 16:01
- 总时间点: 45个
- 数据文件总数: 234个
```

### 时间段分析
```
============================================================
时间段: 09:32
============================================================

📈 涨停池情况:
  - 涨停股票数量: 23只
  - 热门行业: 专用设备 (3只)
  - 代表股票: 中远海科(002401) 德固特(300950) 市北高新(600604) 

💰 资金流向:
  - 主力净流入前3: 中核科技 思源电气 凯撒旅业 

📰 重要新闻:
  - 我国高温将达近期鼎盛 南方降雨减少减弱...
  - 两市融资余额增加21.67亿元...

🚀 异动股票:
  - 大笔买入: 15只 (中核科技, 思源电气, 凯撒旅业...)
  - 有大买盘: 12只 (东山精密, 天齐锂业, 奥飞数据...)

🔄 相比上一时段变化:
  - 新增涨停: 3只
  - 失去涨停: 1只
```

### 全天总结
```
================================================================================
                           全天总结
================================================================================

📈 涨停池统计:
  - 全天涨停股票总数: 45只
  - 最高涨停数量: 28只 (时间: 10:42)

🔥 最活跃时间段:
  1. 10:42 (活跃度: 85)
  2. 09:46 (活跃度: 72)
  3. 13:28 (活跃度: 68)
  4. 14:32 (活跃度: 65)
  5. 11:17 (活跃度: 61)

📝 分析建议:
  - 关注10:42时段的市场动向，涨停股票最多
  - 重点关注活跃时间段的资金流向变化
  - 建议复盘全天45只涨停股票的表现
```

## 文件夹结构要求

确保您的数据文件夹结构如下：

```
fund_data/
├── 2025-07-14/
│   ├── 09-32_fund_flow_tpdog.csv
│   ├── 09-32_concept_fund_flow_tpdog.csv
│   ├── 09-32_zt_pool.csv
│   ├── 09-33_news_cls.csv
│   ├── 09-33_movers_大笔买入.csv
│   └── ...
├── 2025-07-15/
│   └── ...
└── ...
```

## 依赖库

```bash
pip install pandas numpy
```

## 注意事项

1. **文件编码**：脚本会自动尝试UTF-8、GBK、GB2312编码
2. **时间格式**：文件名中的时间必须是HH-MM格式
3. **数据完整性**：缺失的数据文件不会影响其他数据的分析
4. **内存使用**：大量数据文件可能需要较多内存

## 扩展功能

您可以根据需要扩展以下功能：

1. **添加新的数据类型**：在`file_types`字典中添加新的文件模式
2. **自定义分析指标**：修改各个分析函数添加新的统计指标
3. **图表生成**：集成matplotlib生成可视化图表
4. **数据库存储**：将分析结果存储到数据库中
5. **实时监控**：结合定时任务实现实时数据分析

## 故障排除

### 常见问题

1. **文件夹不存在**
   - 检查`ANALYSIS_DATE`和`FUND_DATA_DIR`配置是否正确

2. **编码错误**
   - 确保CSV文件使用UTF-8、GBK或GB2312编码

3. **数据格式错误**
   - 检查CSV文件是否包含必要的列（如"代码"、"名称"等）

4. **内存不足**
   - 减少同时处理的文件数量或增加系统内存

### 调试模式

在脚本中添加调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

这样可以看到详细的处理过程和错误信息。
