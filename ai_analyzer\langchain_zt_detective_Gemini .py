#!/usr/bin/.env python3
# -*- coding: utf-8 -*-
"""
LangChain 涨停侦探 (LangChain ZtDetective) v2.0
- 修复版 & 增强版
- 引擎升级：完全移植 zt_detective.py 的成熟数据加载与分类引擎。
- 环境集成：集成 Gemini.py 的 ..env 和代理配置，更安全、更灵活。
- 工具增强：赋予AI侦探查询龙虎榜、技术指标、大宗交易等更全面的调查能力。
- 动态推理：AI根据上一步的发现，动态决定下一步的调查方向。
- 深度报告：生成由AI推理、人类语言组织的结构化分析报告。
"""

import os
import pandas as pd
import re
from collections import defaultdict
from datetime import datetime, timedelta
import warnings

# --- 核心依赖导入 ---
# LangChain
from langchain.agents import initialize_agent, AgentType
from langchain.tools import tool
from langchain_google_genai import ChatGoogleGenerativeAI

# 环境配置 (从Gemini.py移植)
from dotenv import load_dotenv

warnings.filterwarnings('ignore')

# --- 配置区 (从 zt_detective.py 和 Gemini.py 整合) ---

# 1. 加载 ..env 文件中的环境变量
load_dotenv()

# 2. 检查并获取API密钥
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
if not GOOGLE_API_KEY:
    print("❌ 错误: 未在 ..env 文件中找到 GOOGLE_API_KEY。请创建 ..env 文件并添加 GOOGLE_API_KEY='your_key'。")
    exit()
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY

# 3. 配置代理 (可选，从Gemini.py移植)
USE_PROXY = os.getenv("USE_PROXY", 'False').lower() in ('true', '1', 't')
PROXY_URL = os.getenv("PROXY_URL")
if USE_PROXY and PROXY_URL:
    os.environ['http_proxy'] = PROXY_URL
    os.environ['https_proxy'] = PROXY_URL
    print(f"✅ 已配置代理: {PROXY_URL}")

# 4. 路径配置 (从zt_detective.py移植)
ANALYSIS_DATE = "2025-07-25"
FUND_DATA_DIR = "../fund_data"  # 盘中时间线数据
POST_MARKET_DATA_DIR = "D:\\stock_dev\\zt_any\\data"  # 盘后静态数据
OUTPUT_DIR = "../reports_langchain_v2"


# --- 配置区结束 ---


class ZtToolbox:
    """
    一个工具箱类，负责加载所有数据，并提供一系列被LangChain Agent调用的“工具”方法。
    这个类的逻辑完全基于 zt_detective.py，确保了数据加载的完整性和鲁棒性。
    """

    def __init__(self, analysis_date, data_dir, post_market_data_dir):
        print("初始化工具箱...")
        self.analysis_date = analysis_date
        # --- 今日数据路径 ---
        self.data_dir = os.path.join(data_dir, analysis_date)
        self.post_market_data_dir = os.path.join(post_market_data_dir, analysis_date.replace('-', ''))

        # --- 昨日数据路径 ---
        yesterday = datetime.strptime(analysis_date, '%Y-%m-%d') - timedelta(days=1)
        self.yesterday_str_hyphen = yesterday.strftime('%Y-%m-%d')
        self.yesterday_data_dir = os.path.join(data_dir, self.yesterday_str_hyphen)
        self.yesterday_post_market_data_dir = os.path.join(post_market_data_dir, yesterday.strftime('%Y%m%d'))

        # --- 数据存储 ---
        self.all_day_data = defaultdict(lambda: defaultdict(list))
        self.post_market_data = {}
        self.yesterday_data = {'intraday': defaultdict(list), 'post_market': {}}

        # --- 完整的文件分类字典 (从zt_detective.py移植) ---
        self.file_types = {
            'fund_flow': ['fund_flow_tpdog.csv', 'ths_fund_flow.csv', 'fund_flow_akshare.csv'],
            'concept_flow': ['concept_fund_flow_tpdog.csv', 'concept_fund_flow_akshare.csv', 'concept_fund_flow_'],
            'sector_flow': ['sector_fund_flow_tpdog.csv', 'sector_fund_flow_akshare.csv', 'sector_fund_flow_rank_'],
            'zt_pool': ['zt_pool.csv', 'previous_zt_pool.csv', '涨停股池_akshare_东方财富_', '涨停股池_tpdog_'],
            'zb_pool': ['炸板股池_akshare_东方财富_'],
            'news': ['news_cls.csv', 'news_em.csv', 'news_ths.csv'],
            'index': ['index_sh000001.csv', 'index_sz399006.csv'],
            'movers': ['movers_大笔买入.csv', 'movers_有大买盘.csv'],
            'big_deal': ['ths_big_deal.csv', 'big_deal_'],
            'board_changes': ['board_changes.csv'],
            'market_flow': ['market_fund_flow.csv', 'market_fund_flow_'],
            'industry_board': ['industry_board_ths.csv', 'industry_board_em.csv', 'industry_board_akshare.csv'],
            'indicator': ['indicator_创月新高.csv'],
            'individual_flow': ['individual_fund_flow_'],
            'lhb_detail': ['龙虎榜详情_当日.csv'], # zt_detective中分散在各处，这里统一
            'lhb': ['lhb_jgmmtj.csv'],
            'dzjy': ['dzjy_mrmx.csv'],
            'notices': ['stock_notices.txt'],
            'sector_summary': ['sector_summary_'],
            'acceleration_signals': ['acceleration_signals_'],
            'fund_flow_rank': ['fund_flow_rank_'],
            'main_fund_flow': ['main_fund_flow_'],
            'stock_notifications': ['stock_notifications.csv'],
            'stock_signals': ['stock_signals.csv'],
            'cxg': ['创新高_前100.csv'], 'lxsz': ['连续上涨_全部.csv'], 'cxfl': ['持续放量_前100.csv'],
            'ljqs': ['量价齐升_全部.csv'], 'xstp_20ma': ['向上突破_20MA.csv'],
            'bulk_trades': [f'大宗交易每日明细_A股_{self.analysis_date.replace("-", "")}.csv'],
            'performance_report': [f'业绩报表_{self.analysis_date.replace("-", "")[0:4]}0630.csv'],
        }

        self.load_all_available_data()

    def _load_all_day_data(self):
        """
        (从zt_detective移植) 加载当天所有时间戳的数据文件到内存中。
        """
        if not os.path.exists(self.data_dir):
            print(f"  - 警告: 时间线数据目录未找到: {self.data_dir}")
            return False

        for filename in os.listdir(self.data_dir):
            time_key = self._extract_time_key(filename)
            if not time_key: continue

            file_type = self._classify_file(filename)
            if file_type == 'other': continue

            filepath = os.path.join(self.data_dir, filename)
            df = self._load_csv_safe(filepath)
            if df is not None:
                self.all_day_data[time_key][file_type].append(df)
        return bool(self.all_day_data)

    def _load_post_market_data(self):
        """
        (从zt_detective移植) 加载盘后静态数据。
        """
        post_market_files = {
            'zt_pool': '涨停股池.csv', 'lhb_detail': '龙虎榜详情_当日.csv',
            'cxg': '创新高_前100.csv', 'lxsz': '连续上涨_全部.csv',
            'cxfl': '持续放量_前100.csv', 'ljqs': '量价齐升_全部.csv',
            'xstp_20ma': '向上突破_20MA.csv',
            'bulk_trades': f'大宗交易每日明细_A股_{self.analysis_date.replace("-", "")}.csv',
            'performance_report': f'业绩报表_{self.analysis_date.replace("-", "")[0:4]}0630.csv',
        }
        if not os.path.exists(self.post_market_data_dir):
            print(f"  - 警告: 盘后数据目录未找到: {self.post_market_data_dir}")
            return

        for key, filename in post_market_files.items():
            filepath = os.path.join(self.post_market_data_dir, filename)
            if os.path.exists(filepath):
                df = self._load_csv_safe(filepath)
                if df is not None:
                    self.post_market_data[key] = df
            elif '业绩' in key:  # 兼容财报
                for quarter in ['0331', '0930', '1231']:
                    alt_filename = filename.replace('0630', quarter)
                    alt_filepath = os.path.join(self.post_market_data_dir, alt_filename)
                    if os.path.exists(alt_filepath):
                        df = self._load_csv_safe(alt_filepath)
                        if df is not None: self.post_market_data[key] = df
                        break

    def _load_yesterday_data(self):
        """
        (从zt_detective移植) 加载前一交易日的所有相关数据。
        """
        # 1. 加载昨日的时间线数据
        if os.path.exists(self.yesterday_data_dir):
            for filename in os.listdir(self.yesterday_data_dir):
                file_type = self._classify_file(filename)
                if file_type == 'other': continue
                filepath = os.path.join(self.yesterday_data_dir, filename)
                df = self._load_csv_safe(filepath)
                if df is not None:
                    time_key = self._extract_time_key(filename) or "unknown_time"
                    self.yesterday_data['intraday'][time_key][file_type].append(df)
            print(f"  - 昨日时间线数据加载完成。")
        else:
            print(f"  - 警告: 昨日时间线数据目录未找到: {self.yesterday_data_dir}")

        # 2. 加载昨日的盘后数据
        post_market_files = {
            'lhb_detail': '龙虎榜详情_当日.csv', 'cxg': '创新高_前100.csv', 'lxsz': '连续上涨_全部.csv',
            'bulk_trades': f'大宗交易每日明细_A股_{os.path.basename(self.yesterday_post_market_data_dir)}.csv'
        }
        if os.path.exists(self.yesterday_post_market_data_dir):
            for key, filename in post_market_files.items():
                filepath = os.path.join(self.yesterday_post_market_data_dir, filename)
                if os.path.exists(filepath):
                    df = self._load_csv_safe(filepath)
                    if df is not None:
                        self.yesterday_data['post_market'][key] = df
            print(f"  - 昨日盘后数据加载完成。")
        else:
            print(f"  - 警告: 昨日盘后数据目录未找到: {self.yesterday_post_market_data_dir}")
    def load_all_available_data(self):
        """一次性加载所有需要用到的当日、盘后及昨日数据"""
        print(f"正在加载 {self.analysis_date} 的时间线数据...")
        self._load_all_day_data()  # 调用独立的盘中数据加载器

        print(f"正在加载 {self.analysis_date} 的盘后静态数据...")
        self._load_post_market_data()  # 调用独立的盘后数据加载器

        print(f"正在加载昨日 ({self.yesterday_str_hyphen}) 的关联数据...")
        self._load_yesterday_data()  # 调用独立的昨日数据加载器

        print("所有数据加载完毕！工具箱准备就绪。")

    def _load_data_from_dir(self, directory, storage, is_post_market=False):
        if not os.path.exists(directory):
            print(f"  - 警告: 数据目录未找到: {directory}")
            return
        for filename in os.listdir(directory):
            file_type = self._classify_file(filename)
            if file_type == 'other': continue

            filepath = os.path.join(directory, filename)
            df = self._load_csv_safe(filepath)
            if df is not None:
                if is_post_market:
                    storage[file_type] = df  # 盘后数据直接存，通常一个类型一个文件
                else:
                    time_key = self._extract_time_key(filename) or "unknown_time"
                    storage[time_key][file_type].append(df)

    def get_target_list(self):
        """读取收盘后的涨停股池文件，确定要分析的目标列表。(从zt_detective.py移植)"""
        df_eod_zt = None
        # 优先从盘后数据中找
        if 'zt_pool' in self.post_market_data:
            print("  - 从盘后数据中找到涨停股池。")
            df_eod_zt = self.post_market_data['zt_pool']

        # 如果盘后没有，再从盘中数据倒序找
        if df_eod_zt is None:
            for time_key in sorted(self.all_day_data.keys(), reverse=True):
                if 'zt_pool' in self.all_day_data[time_key]:
                    # 过滤掉previous_zt_pool.csv等非当日涨停数据
                    valid_dfs = [df for df in self.all_day_data[time_key]['zt_pool'] if '涨停原因' in df.columns]
                    if valid_dfs:
                        print(f"  - 从盘中 {time_key} 数据中找到涨停股池。")
                        df_eod_zt = valid_dfs[0]
                        break

        if df_eod_zt is None or df_eod_zt.empty:
            print("❌ 错误: 未能找到任何来源的涨停股池文件。")
            return []

        # 标准化列名以兼容不同来源
        df_eod_zt.rename(columns={
            "代码": "code", "名称": "name", "涨跌幅": "change_pct",
            "连板数": "consecutive_boards", "炸板次数": "broken_times",
            "最后封板时间": "final_seal_time", "所属行业": "industry"
        }, inplace=True, errors='ignore')

        return df_eod_zt.to_dict('records')

    def _extract_time_key(self, filename):
        match = re.search(r'(\d{2})-(\d{2})_', filename) or \
                re.search(r'_\d{8}_(\d{2})(\d{2})\d{2}', filename) or \
                re.search(r'_(\d{2})(\d{2})\d{2}\.csv$', filename)
        return f"{match.group(1)}:{match.group(2)}" if match else None

    def _classify_file(self, filename):
        all_patterns = []
        for file_type, patterns in self.file_types.items():
            for pattern in patterns:
                all_patterns.append((pattern, file_type))
        all_patterns.sort(key=lambda x: len(x[0]), reverse=True)
        for pattern, file_type in all_patterns:
            if pattern in filename: return file_type
        return 'other'

    def _load_csv_safe(self, filepath):
        if not os.path.exists(filepath): return None
        try:
            return pd.read_csv(filepath, encoding='utf-8', on_bad_lines='skip')
        except:
            try:
                return pd.read_csv(filepath, encoding='gbk', on_bad_lines='skip')
            except:
                return None


# --- 初始化工具箱实例 ---
toolbox = ZtToolbox(
    analysis_date=ANALYSIS_DATE,
    data_dir=FUND_DATA_DIR,
    post_market_data_dir=POST_MARKET_DATA_DIR
)


# --- 定义LangChain工具 (部分工具已增强) ---
@tool
def get_stock_intraday_timeline(stock_code: str, stock_name: str) -> str:
    """
    查询指定股票在当天的详细盘中异动时间线。
    输入股票代码和名称，返回一个按时间排序的事件列表，包括资金流入排名和是否出现大买盘信号。
    这是分析股票当天走势的核心工具。
    """
    global toolbox
    events = defaultdict(list)
    for time_key in sorted(toolbox.all_day_data.keys()):
        if len(time_key) != 5: continue
        data_at_time = toolbox.all_day_data[time_key]

        # 个股资金排名
        if 'individual_flow' in data_at_time:
            df_list = data_at_time['individual_flow']
            if not df_list: continue
            df = pd.concat(df_list)
            df['代码'] = df['代码'].astype(str).str.zfill(6)
            stock_info = df[df['代码'] == stock_code]
            if not stock_info.empty:
                rank = stock_info.index[0] + 1
                if rank <= 50:
                    events[time_key].append(f"个股资金排名冲入前50，位列第{rank}名")

        # 大买盘信号
        if 'movers' in data_at_time:
            for df in data_at_time['movers']:
                if '名称' in df.columns and stock_name in df['名称'].values:
                    events[time_key].append("出现'大买盘'或'大笔买入'信号")
                    break

    if not events: return f"股票 {stock_name}({stock_code}) 在盘中没有捕捉到明显的资金排名或大买盘异动。"
    timeline_str = f"为 {stock_name}({stock_code}) 生成的盘中异动时间线:\n"
    for time, event_list in events.items():
        timeline_str += f"- **{time}**: {', '.join(set(event_list))}\n"
    return timeline_str



@tool
def get_post_market_info(stock_code: str) -> str:
    """
    【增强版】查询指定股票在当天收盘后的关键信息，包括龙虎榜(LHB)、技术形态、大宗交易和业绩。
    这对于判断机构动向、技术共振和基本面驱动至关重要。
    """
    global toolbox
    insights = []
    # 龙虎榜
    if 'lhb_detail' in toolbox.post_market_data:
        lhb_df = toolbox.post_market_data['lhb_detail']
        stock_lhb = lhb_df[lhb_df['代码'].astype(str).str.zfill(6) == stock_code]
        if not stock_lhb.empty:
            reason = stock_lhb['上榜原因'].iloc[0]
            net_buy = stock_lhb['净买额'].iloc[0]
            insights.append(f"龙虎榜: 因'{reason}'上榜，净买额为{net_buy / 10000:.2f}万元。")

    # 技术形态
    tech_signals = []
    tech_map = {'cxg': '创月新高', 'lxsz': '连续上涨', 'cxfl': '持续放量', 'ljqs': '量价齐升',
                'xstp_20ma': '向上突破20日线'}
    for key, signal_name in tech_map.items():
        if key in toolbox.post_market_data:
            df = toolbox.post_market_data[key]
            # 修复：这里的变量名是 stock_code
            if '代码' in df.columns and stock_code in df['代码'].astype(str).str.zfill(6).values:
                tech_signals.append(signal_name)
    if tech_signals:
        insights.append(f"技术信号: 触发了 {', '.join(tech_signals)}。")

    # 大宗交易
    if 'bulk_trades' in toolbox.post_market_data:
        bulk_df = toolbox.post_market_data['bulk_trades']
        stock_bulk = bulk_df[bulk_df['代码'].astype(str).str.zfill(6) == stock_code]
        if not stock_bulk.empty:
            total_amount = stock_bulk['成交额'].sum()
            insights.append(f"大宗交易: 发生{len(stock_bulk)}笔大宗交易，总成交额{total_amount / 10000:.2f}万元。")

    return "\n".join(insights) if insights else f"未找到 {stock_code} 的盘后龙虎榜、技术形态或大宗交易信息。"

@tool
def get_yesterday_clues(stock_code: str) -> str:
    """
    【增强版】回溯查询指定股票在前一交易日的表现，包括是否上过龙虎榜或有无关键技术信号。
    这可以帮助发现上涨是否具有延续性或提前埋伏的迹象。
    """
    global toolbox
    insights = []
    # 昨日龙虎榜
    if 'lhb_detail' in toolbox.yesterday_data['post_market']:
        lhb_df = toolbox.yesterday_data['post_market']['lhb_detail']
        stock_lhb = lhb_df[lhb_df['代码'].astype(str).str.zfill(6) == stock_code]
        if not stock_lhb.empty:
            insights.append("昨日已登上龙虎榜，市场关注度较高。")

    # 昨日技术信号
    tech_signals = []
    tech_map = {'cxg': '创月新高', 'lxsz': '连续上涨'}
    for key, signal_name in tech_map.items():
        if key in toolbox.yesterday_data['post_market']:
            df = toolbox.yesterday_data['post_market'][key]
            if '代码' in df.columns and stock_code in df['代码'].astype(str).str.zfill(6).values:
                tech_signals.append(signal_name)
    if tech_signals:
        insights.append(f"昨日已触发技术信号: {', '.join(tech_signals)}。")

    return "\n".join(insights) if insights else f"在昨日数据中未发现 {stock_code} 的明显线索（龙虎榜、技术信号）。"


def run_zt_detective_agent():
    """
    主函数，用于初始化并运行LangChain Agent。
    """
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    llm = ChatGoogleGenerativeAI(model="gemini-2.5-pro", temperature=0.2, convert_system_message_to_human=True)

    tools = [get_stock_intraday_timeline, get_post_market_info, get_yesterday_clues]

    agent = initialize_agent(
        tools, llm, agent=AgentType.STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION, verbose=True
    )

    agent_prompt = """
    你是中国A股市场顶级的涨停板分析师。你的任务是基于提供的工具，对一只给定的涨停股票进行深入、全面的调查，并生成一份专业的分析报告。

    你的分析应该遵循以下思考路径：
    1.  **历史回溯**: 今天的强势是偶然的吗？首先使用 `get_yesterday_clues` 工具，看看昨天有没有什么预兆，比如已经上过龙虎榜或出现技术突破。
    2.  **自身强度分析**: 接着，使用 `get_stock_intraday_timeline` 工具，检查这只股票本身的盘中表现。它的资金是在什么时间点开始异动的？有没有关键的“大买盘”信号？
    3.  **盘后验证**: 收盘后市场对它的态度如何？使用 `get_post_market_info` 工具，检查有没有龙虎榜数据、技术形态共振或大宗交易。这决定了第二天的预期。
    4.  **综合归因与总结**: 综合以上所有信息，形成你的最终结论。清晰地指出这只股票涨停的核心驱动因素是什么（例如：昨日强者恒强+今日资金持续攻击、板块效应发酵+盘后技术信号确认、午后消息刺激+机构龙虎榜买入等），并用人类易于理解的语言，以Markdown格式输出一份完整的分析报告。

    报告应包含以下部分：
    - **股票基本信息**: 代码，名称。
    - **核心驱动因素总结**: 一句话概括你认为的核心原因。
    - **关键证据链**: 详细罗列你从工具中找到的所有关键证据（昨日、今日盘中、今日盘后）。
    - **最终结论与展望**: 总结你的发现，并对次日走势做出简单展望。
    """

    target_stocks = toolbox.get_target_list()
    if not target_stocks:
        return

    print(f"\n🕵️‍♂️ AI涨停侦探开始工作，分析 {len(target_stocks)} 只股票...")

    for stock in target_stocks[:3]:
        code = str(stock.get('code', '')).zfill(6)
        name = stock.get('name', '未知名称')

        print(f"\n\n===== 正在分析: {name} ({code}) =====\n")

        query = f"股票代码: {code}, 股票名称: {name}。\n请遵循系统指令，为这只股票生成一份详细的涨停分析报告。"
        final_query_with_prompt = agent_prompt + "\n\n" + query

        try:
            result = agent.run(final_query_with_prompt)

            filename = os.path.join(OUTPUT_DIR, f"langchain_report_v2_{code}_{name}.md")
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(result)
            print(f"✅ 报告已生成: {filename}")
        except Exception as e:
            print(f"❌ 分析 {name} 时发生错误: {e}")


if __name__ == "__main__":
    run_zt_detective_agent()