#!/usr/bin/.env python3
# -*- coding: utf-8 -*-
"""
基金数据时间序列分析器 (v2.0 - 大师版)
按时间段分析fund_data文件夹中的数据，生成一天的详细报告
- 新增焦点板块生命周期追踪与复盘
- 增强时间段对比逻辑，可识别排名与资金动能变化
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import glob
import re
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# 配置参数
ANALYSIS_DATE = "2025-07-30"  # 要分析的日期，可在此修改
FUND_DATA_DIR = "../fund_data"
OUTPUT_DIR = "../reports"

# 开盘时间配置
TRADING_HOURS = [
    (9, 15, 11, 35),   # 上午：9:15-11:35 (包含集合竞价和收盘后数据)
    (13, 0, 15, 5),    # 下午：13:00-15:05 (包含收盘后数据)
    (16, 0, 23, 59)    # 龙虎榜等：16:00-23:59
]

# 排除的文件类型配置（这些文件不会被分析）
EXCLUDED_FILES = [
    # 'stock_signals.csv'  # 股票信号数据现在包含在分析中
]

class FundDataAnalyzer:
    def __init__(self, analysis_date, data_dir):
        self.analysis_date = analysis_date
        self.data_dir = data_dir
        self.date_dir = os.path.join(data_dir, analysis_date)
        self.time_data = defaultdict(dict)
        self.file_types = {
            'fund_flow': ['fund_flow_tpdog.csv', 'ths_fund_flow.csv', 'fund_flow_akshare.csv', 'fund_flow_ths.csv'],
            'concept_flow': ['concept_fund_flow_tpdog.csv', 'concept_fund_flow_akshare.csv', 'concept_fund_flow_', '实时概念资金流_'],
            'sector_flow': ['sector_fund_flow_tpdog.csv', 'sector_fund_flow_akshare.csv', 'sector_fund_flow_rank_'],
            'zt_pool': [
                'zt_pool.csv', 'previous_zt_pool.csv', '涨停股池_akshare_东方财富_', '涨停股池_tpdog_', 'limit_up_pool_',
                'zt_pool_', '_zt_pool.csv'  # 支持 zt_pool_20250730_150228.csv 和 14-57_zt_pool.csv 格式
            ],
            'zt_pool_previous': ['zt_pool_previous_', 'previous_zt_pool'],
            'dt_pool': ['dt_pool_', 'dt_pool.csv', '跌停股池_'],
            'zb_pool': ['炸板股池_akshare_东方财富_', 'zb_pool_', 'zb_pool.csv', '炸板股池_'],
            'big_purchase': ['big_purchase_unusual_', 'big_purchase_', '大笔买入_unusual_', '大笔买入_'],
            'big_buy': ['big_buy_unusual_', 'big_buy_', '大买盘_unusual_', '大买盘_', '有大买盘_'],
            'news': ['news_cls.csv', 'news_em.csv', 'news_ths.csv'],
            'index': ['index_sh000001.csv', 'index_sz399006.csv'],
            'movers': ['movers_大笔买入.csv', 'movers_有大买盘.csv'],
            'big_deal': ['ths_big_deal.csv', 'big_deal_'],
            'board_changes': ['board_changes.csv'],
            'market_flow': ['market_fund_flow.csv', 'market_fund_flow_'],
            'industry_board': ['industry_board_ths.csv', 'industry_board_em.csv', 'industry_board_akshare.csv'],
            'indicator': ['indicator_创月新高.csv'],
            'individual_flow': ['individual_fund_flow_', '股票资金流_zssh_', '股票资金流_zssz_', '股票资金流_zsbj_'],
            'lhb': ['lhb_jgmmtj.csv'],
            'dzjy': ['dzjy_mrmx.csv'],
            'notices': ['stock_notices.txt'],
            'sector_summary': ['sector_summary_'],
            'acceleration_signals': ['acceleration_signals_'],
            'fund_flow_rank': ['fund_flow_rank_'],
            'main_fund_flow': ['main_fund_flow_'],
            'stock_notifications': ['stock_notifications.csv'],
            'stock_signals': ['stock_signals.csv']
        }
        # [核心升级] 引入生命周期追踪器
        self.focus_tracker = {'concepts': {}, 'sectors': {}}
        # [核心升级] 引入“首次出现”追踪器
        self.first_appearance_tracker = {'concepts': set(), 'sectors': set()}
        # [核心升级] 引入昨日快照存储, 升级为字典以存储连板数
        self.yesterday_snapshot = {'zt_pool': {}, 'concept_top3': set(), 'sector_top3': set()}
        # [新增] 资金流动态追踪器 - 用于对比排名和金额变化
        self.flow_tracker = {
            'fund_flow': {},      # 个股资金流
            'concept_flow': {},   # 概念板块资金流
            'sector_flow': {}     # 行业板块资金流
        }
        # 删除报告存储
        self.deletion_report = []


    def is_trading_time(self, hour, minute):
        """检查给定时间是否在交易时间内"""
        for start_h, start_m, end_h, end_m in TRADING_HOURS:
            start_time = start_h * 60 + start_m
            end_time = end_h * 60 + end_m
            current_time = hour * 60 + minute

            if start_time <= current_time <= end_time:
                return True
        return False

    def is_excluded_file(self, filename):
        """检查文件是否在排除列表中"""
        for excluded in EXCLUDED_FILES:
            if excluded in filename:
                return True
        return False

    def clean_non_trading_files(self):
        """删除非开盘时间的数据文件，生成删除报告"""
        if not os.path.exists(self.date_dir):
            return

        files = os.listdir(self.date_dir)
        deleted_files = []

        for file in files:
            if not (file.endswith('.csv') or file.endswith('.txt')):
                continue

            # 检查是否是排除的文件类型
            if self.is_excluded_file(file):
                continue

            # 提取时间信息
            time_match = None
            hour, minute = None, None

            # 优先匹配更具体的格式，避免误匹配

            # 格式3: _YYYY-MM-DD_HH-MM-SS (带分隔符的新格式) - 最具体，优先匹配
            time_match = re.search(r'_\d{4}-\d{2}-\d{2}_(\d{2})-(\d{2})-(\d{2})\.', file)
            if time_match:
                hour, minute = int(time_match.groups()[0]), int(time_match.groups()[1])
            else:
                # 格式2: _YYYYMMDD_HHMMSS (标准新格式)
                time_match = re.search(r'_\d{8}_(\d{2})(\d{2})(\d{2})\.', file)
                if time_match:
                    hour, minute = int(time_match.groups()[0]), int(time_match.groups()[1])
                else:
                    # 格式1: HH-MM_ (旧格式) - 最后匹配，避免误匹配日期中的数字
                    time_match = re.search(r'^(\d{2})-(\d{2})_', file)  # 添加^确保从文件名开头匹配
                    if time_match:
                        hour, minute = int(time_match.groups()[0]), int(time_match.groups()[1])

            # 处理时间提取结果
            if hour is not None and minute is not None:
                # 成功提取到时间，检查是否在交易时间内
                if not self.is_trading_time(hour, minute):
                    file_path = os.path.join(self.date_dir, file)
                    try:
                        os.remove(file_path)
                        deleted_files.append({
                            'file': file,
                            'time': f"{hour:02d}:{minute:02d}",
                            'reason': '非交易时间'
                        })
                        print(f"已删除非交易时间文件: {file} (时间: {hour:02d}:{minute:02d})")
                    except Exception as e:
                        print(f"删除文件失败 {file}: {e}")
                else:
                    print(f"保留交易时间文件: {file} (时间: {hour:02d}:{minute:02d})")
            else:
                # 无法提取时间，保留文件（安全策略）
                print(f"保留文件（无法识别时间格式）: {file}")

        # 生成删除报告
        if deleted_files:
            self.generate_deletion_report(deleted_files)

        return len(deleted_files)

    def generate_deletion_report(self, deleted_files):
        """生成删除报告"""
        report_content = f"""
# {self.analysis_date} 数据清理报告

## 删除统计
- 删除文件总数: {len(deleted_files)}个
- 删除原因: 非交易时间数据

## 交易时间规则
- 上午交易: 09:15-11:35
- 下午交易: 13:00-15:05
- 龙虎榜等: 16:00-23:59

## 删除文件详情
"""

        for item in deleted_files:
            report_content += f"- {item['file']} (时间: {item['time']}, 原因: {item['reason']})\n"

        # 保存删除报告到当天文件夹
        report_path = os.path.join(self.date_dir, f"deletion_report_{self.analysis_date}.md")
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"删除报告已生成: {report_path}")
        except Exception as e:
            print(f"生成删除报告失败: {e}")

    def scan_files(self):
        """扫描指定日期文件夹中的所有文件"""
        if not os.path.exists(self.date_dir):
            print(f"错误：日期文件夹 {self.date_dir} 不存在")
            return False
            
        files = os.listdir(self.date_dir)
        print(f"发现 {len(files)} 个文件")
        
        # 按时间分组文件
        for file in files:
            if file.endswith('.csv') or file.endswith('.txt'):
                # 检查是否是排除的文件类型
                if self.is_excluded_file(file):
                    print(f"跳过排除的文件: {file}")
                    continue

                time_key = None

                # 提取时间信息 - 支持多种格式
                # 格式1: HH-MM_ (旧格式) - 确保不匹配日期格式
                time_match = re.search(r'^(\d{2})-(\d{2})_', file)
                if time_match:
                    hour, minute = time_match.groups()
                    # 验证是否为有效时间（避免匹配日期）
                    if 0 <= int(hour) <= 23 and 0 <= int(minute) <= 59:
                        time_key = f"{hour}:{minute}"
                else:
                    # 格式2: _YYYYMMDD_HHMMSS (新格式)
                    time_match = re.search(r'_\d{8}_(\d{2})(\d{2})(\d{2})\.', file)
                    if time_match:
                        hour, minute, second = time_match.groups()
                        time_key = f"{hour}:{minute}"
                    else:
                        # 格式3: _HHMMSS.csv (新格式，如涨停股池_akshare_东方财富_093651.csv)
                        time_match = re.search(r'_(\d{2})(\d{2})(\d{2})\.csv$', file)
                        if time_match:
                            hour, minute, second = time_match.groups()
                            time_key = f"{hour}:{minute}"

                # 如果没有时间戳，使用特殊的"全天"分组
                if not time_key:
                    time_key = "全天数据"

                if time_key not in self.time_data:
                    self.time_data[time_key] = {}

                # 分类文件
                file_type = self.classify_file(file)
                if file_type not in self.time_data[time_key]:
                    self.time_data[time_key][file_type] = []
                self.time_data[time_key][file_type].append(file)
        
        print(f"按时间分组后，共有 {len(self.time_data)} 个时间点")
        return True
    
    def classify_file(self, filename):
        """根据文件名分类文件类型"""
        all_patterns = []
        for file_type, patterns in self.file_types.items():
            for pattern in patterns:
                all_patterns.append((pattern, file_type))
        all_patterns.sort(key=lambda x: len(x[0]), reverse=True)

        for pattern, file_type in all_patterns:
            if pattern in filename:
                return file_type
        return 'other'
    
    def load_csv_safe(self, filepath):
        """安全加载CSV文件"""
        try:
            for encoding in ['utf-8', 'gbk', 'gb2312']:
                try:
                    # 先尝试正常读取
                    df = pd.read_csv(filepath, encoding=encoding)
                    return df
                except (UnicodeDecodeError, pd.errors.ParserError):
                    try:
                        # 如果解析错误，尝试使用更宽松的参数
                        df = pd.read_csv(filepath, encoding=encoding,
                                       on_bad_lines='skip', engine='python')
                        return df
                    except:
                        continue
                except Exception:
                    continue
            return None
        except Exception as e:
            print(f"加载文件失败 {filepath}: {e}")
            return None
    
    def analyze_time_period(self, time_key, files_dict):
        """分析特定时间段的数据"""
        analysis = {'time': time_key, 'details': {}}
        
        # 分析资金流向
        if 'fund_flow' in files_dict:
            analysis['details']['fund_flow'] = self.analyze_fund_flow(time_key, files_dict['fund_flow'])
        if 'zt_pool' in files_dict:
            analysis['details']['zt_pool'] = self.analyze_zt_pool(time_key, files_dict['zt_pool'])
        if 'zt_pool_previous' in files_dict:
            analysis['details']['zt_pool_previous'] = self.analyze_zt_pool(time_key, files_dict['zt_pool_previous'])
        if 'dt_pool' in files_dict:
            analysis['details']['dt_pool'] = self.analyze_dt_pool(time_key, files_dict['dt_pool'])
        if 'zb_pool' in files_dict:
            analysis['details']['zb_pool'] = self.analyze_zb_pool(time_key, files_dict['zb_pool'])
        if 'big_purchase' in files_dict:
            analysis['details']['big_purchase'] = self.analyze_big_purchase(time_key, files_dict['big_purchase'])
        if 'big_buy' in files_dict:
            analysis['details']['big_buy'] = self.analyze_big_buy(time_key, files_dict['big_buy'])
        if 'news' in files_dict:
            analysis['details']['news'] = self.analyze_news(time_key, files_dict['news'])
        if 'concept_flow' in files_dict:
            analysis['details']['concept_flow'] = self.analyze_concept_flow(time_key, files_dict['concept_flow'])
        if 'sector_flow' in files_dict:
            analysis['details']['sector_flow'] = self.analyze_sector_flow(time_key, files_dict['sector_flow'])
        if 'movers' in files_dict:
            analysis['details']['movers'] = self.analyze_movers(time_key, files_dict['movers'])
        if 'sector_summary' in files_dict:
            analysis['details']['sector_summary'] = self.analyze_sector_summary(time_key, files_dict['sector_summary'])
        if 'acceleration_signals' in files_dict:
            analysis['details']['acceleration_signals'] = self.analyze_acceleration_signals(time_key, files_dict['acceleration_signals'])
        if 'big_deal' in files_dict:
            analysis['details']['big_deal'] = self.analyze_big_deal(time_key, files_dict['big_deal'])
        if 'fund_flow_rank' in files_dict:
            analysis['details']['fund_flow_rank'] = self.analyze_fund_flow_rank(time_key, files_dict['fund_flow_rank'])
        if 'main_fund_flow' in files_dict:
            analysis['details']['main_fund_flow'] = self.analyze_main_fund_flow(time_key, files_dict['main_fund_flow'])
        if 'stock_notifications' in files_dict:
            analysis['details']['stock_notifications'] = self.analyze_stock_notifications(time_key, files_dict['stock_notifications'])
        if 'individual_flow' in files_dict:
            analysis['details']['individual_flow'] = self.analyze_individual_flow(time_key, files_dict['individual_flow'])
        if 'index' in files_dict:
            analysis['details']['index'] = self.analyze_index(time_key, files_dict['index'])
        if 'board_changes' in files_dict:
            analysis['details']['board_changes'] = self.analyze_board_changes(time_key, files_dict['board_changes'])
        if 'market_flow' in files_dict:
            analysis['details']['market_flow'] = self.analyze_market_flow(time_key, files_dict['market_flow'])
        if 'industry_board' in files_dict:
            analysis['details']['industry_board'] = self.analyze_industry_board(time_key, files_dict['industry_board'])
        if 'indicator' in files_dict:
            analysis['details']['indicator'] = self.analyze_indicator(time_key, files_dict['indicator'])
        if 'lhb' in files_dict:
            analysis['details']['lhb'] = self.analyze_lhb(time_key, files_dict['lhb'])
        if 'dzjy' in files_dict:
            analysis['details']['dzjy'] = self.analyze_dzjy(time_key, files_dict['dzjy'])
        if 'notices' in files_dict:
            analysis['details']['notices'] = self.analyze_notices(time_key, files_dict['notices'])
        if 'stock_signals' in files_dict:
            analysis['details']['stock_signals'] = self.analyze_stock_signals(time_key, files_dict['stock_signals'])

        return analysis

    def analyze_fund_flow(self, time_key, files):
        """分析资金流向数据"""
        analysis = {'top_inflow': [], 'top_outflow': [], 'total_stats': {}, 'full_data': pd.DataFrame()}
        all_dfs = []
        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            # 支持多种列名格式：code/代码, name/名称
            if df is not None and not df.empty:
                # 检查是否包含代码列（支持 'code' 或 '代码'）
                code_cols = [col for col in df.columns if col in ['code', '代码', '股票代码']]
                if code_cols:
                    all_dfs.append(df)

        # 如果没有任何一个文件包含有效数据，则直接返回空结果
        if not all_dfs:
            return analysis

        # 合并数据
        full_df = pd.concat(all_dfs, ignore_index=True)

        # 标准化列名
        if 'code' in full_df.columns and '代码' not in full_df.columns:
            full_df['代码'] = full_df['code']
        if 'name' in full_df.columns and '名称' not in full_df.columns:
            full_df['名称'] = full_df['name']

        # 去重（基于代码列）
        code_col = '代码' if '代码' in full_df.columns else 'code'
        full_df = full_df.drop_duplicates(subset=[code_col]).reset_index(drop=True)

        # 查找净流入列 - 优先使用main_net_inflow
        net_flow_cols = []
        if 'main_net_inflow' in full_df.columns:
            net_flow_cols = ['main_net_inflow']
        elif 'net_inflow' in full_df.columns:
            net_flow_cols = ['net_inflow']
        else:
            # 查找中文净流入列
            net_flow_cols = [col for col in full_df.columns if '净' in col and ('流入' in col or '额' in col) and '总' not in col]

        if not net_flow_cols:
            return analysis

        net_col = net_flow_cols[0]
        full_df[net_col] = pd.to_numeric(full_df[net_col], errors='coerce').fillna(0)

        # 确保有必要的列
        name_col = '名称' if '名称' in full_df.columns else 'name'
        required_cols = [code_col, name_col, net_col]

        analysis['top_inflow'] = full_df.nlargest(5, net_col)[required_cols].to_dict('records')
        analysis['top_outflow'] = full_df.nsmallest(5, net_col)[required_cols].to_dict('records')
        analysis['full_data'] = full_df
        return analysis

    def analyze_zt_pool(self, time_key, files):
        """分析涨停池数据 - 支持多种格式"""
        analysis = {'zt_stocks': [], 'stats': {}}
        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                # 标准化列名，支持不同数据源
                zt_stocks = []
                for _, row in df.iterrows():
                    stock_info = {}

                    # 基本信息
                    stock_info['代码'] = str(row.get('代码', ''))
                    stock_info['名称'] = str(row.get('名称', ''))
                    stock_info['所属行业'] = str(row.get('所属行业', ''))

                    # 价格信息
                    stock_info['最新价'] = row.get('最新价', 0)
                    stock_info['涨跌幅'] = row.get('涨跌幅', 0)

                    # 连板信息
                    if '连板数' in row:
                        stock_info['连板数'] = row['连板数']
                    elif 'l_info' in row:
                        # tpdog格式：解析l_info字段 (如 "2/2")
                        l_info = str(row['l_info'])
                        if '/' in l_info:
                            stock_info['连板数'] = int(l_info.split('/')[0])
                        else:
                            stock_info['连板数'] = 1
                    else:
                        stock_info['连板数'] = 1

                    # 封板信息
                    stock_info['首次封板时间'] = str(row.get('首次封板时间', ''))
                    stock_info['炸板次数'] = row.get('炸板次数', 0)

                    # 成交信息
                    stock_info['成交额'] = row.get('成交额', 0)
                    stock_info['换手率'] = row.get('换手率', 0)

                    zt_stocks.append(stock_info)

                analysis['zt_stocks'] = zt_stocks
                analysis['stats'] = {
                    'total_zt': len(df),
                    'industries': df['所属行业'].value_counts().to_dict() if '所属行业' in df.columns else {},
                    'avg_board_count': sum(s['连板数'] for s in zt_stocks) / len(zt_stocks) if zt_stocks else 0,
                    'board_distribution': self._get_board_distribution(zt_stocks)
                }
                break # 通常只分析一个涨停池文件
        return analysis

    def _get_board_distribution(self, zt_stocks):
        """获取连板分布"""
        board_dist = {}
        for stock in zt_stocks:
            board_count = stock.get('连板数', 1)
            board_key = f"{board_count}板"
            board_dist[board_key] = board_dist.get(board_key, 0) + 1
        return board_dist

    def analyze_dt_pool(self, time_key, files):
        """分析跌停股池数据"""
        analysis = {'dt_stocks': [], 'stats': {}}
        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                # 标准化跌停股数据
                dt_stocks = []
                for _, row in df.iterrows():
                    stock_info = {}

                    # 基本信息
                    stock_info['代码'] = str(row.get('代码', ''))
                    stock_info['名称'] = str(row.get('名称', ''))
                    stock_info['所属行业'] = str(row.get('所属行业', ''))

                    # 价格信息
                    stock_info['最新价'] = row.get('最新价', 0)
                    stock_info['涨跌幅'] = row.get('涨跌幅', 0)
                    stock_info['跌停价'] = row.get('跌停价', 0)

                    # 跌停信息
                    stock_info['首次跌停时间'] = str(row.get('首次跌停时间', ''))
                    stock_info['炸板次数'] = row.get('炸板次数', 0)

                    # 成交信息
                    stock_info['成交额'] = row.get('成交额', 0)
                    stock_info['换手率'] = row.get('换手率', 0)
                    stock_info['振幅'] = row.get('振幅', 0)

                    dt_stocks.append(stock_info)

                analysis['dt_stocks'] = dt_stocks
                analysis['stats'] = {
                    'total_dt': len(df),
                    'industries': df['所属行业'].value_counts().to_dict() if '所属行业' in df.columns else {}
                }
                break
        return analysis

    def analyze_zb_pool(self, time_key, files):
        """分析炸板股池数据"""
        analysis = {'zb_stocks': [], 'stats': {}}
        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                # 首先判断是否为真正的炸板股池文件
                # 必须包含"炸板次数"或"炸板"列
                zb_columns = [col for col in df.columns if '炸板' in col]
                if not zb_columns:
                    # 如果没有炸板相关列，跳过这个文件
                    continue

                # 标准化炸板股数据
                zb_stocks = []
                for _, row in df.iterrows():
                    stock_info = {}

                    # 基本信息
                    stock_info['代码'] = str(row.get('代码', ''))
                    stock_info['名称'] = str(row.get('名称', ''))
                    stock_info['所属行业'] = str(row.get('所属行业', ''))

                    # 价格信息
                    stock_info['最新价'] = row.get('最新价', 0)
                    stock_info['涨跌幅'] = row.get('涨跌幅', 0)
                    stock_info['涨停价'] = row.get('涨停价', 0)

                    # 炸板信息
                    stock_info['首次封板时间'] = str(row.get('首次封板时间', ''))
                    stock_info['炸板次数'] = row.get('炸板次数', 0)

                    # 成交信息
                    stock_info['成交额'] = row.get('成交额', 0)
                    stock_info['换手率'] = row.get('换手率', 0)
                    stock_info['振幅'] = row.get('振幅', 0)

                    zb_stocks.append(stock_info)

                analysis['zb_stocks'] = zb_stocks
                analysis['stats'] = {
                    'total_zb': len(df),
                    'industries': df['所属行业'].value_counts().to_dict() if '所属行业' in df.columns else {},
                    'avg_amplitude': sum(s.get('振幅', 0) for s in zb_stocks) / len(zb_stocks) if zb_stocks else 0
                }
                break
        return analysis

    def analyze_news(self, time_key, files):
        """分析新闻数据"""
        # ... 此函数无重大修改 ...
        analysis = {'news_list': []}
        titles = set()
        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                for _, row in df.iterrows():
                    title = str(row.get('标题', '')).strip()
                    if title and title not in titles:
                        analysis['news_list'].append({'title': title})
                        titles.add(title)
        return analysis

    def _analyze_board_flow(self, files):
        """通用函数分析板块资金流"""
        analysis = {'top_inflow': [], 'top_outflow': [], 'full_data': pd.DataFrame()}
        all_dfs = []
        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                all_dfs.append(df)
        if not all_dfs: return analysis

        # 智能识别名称列
        full_df = pd.concat(all_dfs, ignore_index=True)
        name_col = None
        for col in ['名称', '行业', '板块名称', '概念名称']:
            if col in full_df.columns:
                name_col = col
                break

        if name_col:
            full_df = full_df.drop_duplicates(subset=[name_col]).reset_index(drop=True)
        else:
            full_df = full_df.drop_duplicates().reset_index(drop=True)

        net_flow_cols = [col for col in full_df.columns if '净' in col and ('流入' in col or '额' in col)]
        if not net_flow_cols: return analysis

        net_col = net_flow_cols[0]
        full_df[net_col] = pd.to_numeric(full_df[net_col], errors='coerce').fillna(0)

        analysis['top_inflow'] = full_df.nlargest(20, net_col).to_dict('records') # 追踪前20
        analysis['top_outflow'] = full_df.nsmallest(10, net_col).to_dict('records')
        analysis['full_data'] = full_df
        return analysis

    def analyze_concept_flow(self, time_key, files):
        return self._analyze_board_flow(files)

    def analyze_sector_flow(self, time_key, files):
        return self._analyze_board_flow(files)

    def analyze_movers(self, time_key, files):
        """分析异动股票"""
        # ... 此函数无重大修改 ...
        analysis = {'movers': []}
        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                mover_type = file.split('_')[-1].replace('.csv', '')
                for _, row in df.iterrows():
                    analysis['movers'].append({
                        'type': mover_type, 'name': row.get('名称', '')
                    })
        return analysis

    def analyze_sector_summary(self, time_key, files):
        """分析行业板块汇总数据"""
        analysis = {'sector_summaries': [], 'stats': {}}
        sector_data = {}

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                # 从文件名提取行业名称
                sector_name = file.split('_')[2] if len(file.split('_')) > 2 else 'unknown'
                if sector_name not in sector_data:
                    sector_data[sector_name] = []
                sector_data[sector_name].append(df)

        # 合并同一行业的数据
        for sector_name, dfs in sector_data.items():
            if dfs:
                combined_df = pd.concat(dfs).drop_duplicates().reset_index(drop=True)
                analysis['sector_summaries'].append({
                    'sector': sector_name,
                    'count': len(combined_df),
                    'data': combined_df.head(10).to_dict('records') if not combined_df.empty else []
                })

        analysis['stats'] = {
            'total_sectors': len(sector_data),
            'total_files': len(files)
        }
        return analysis

    def analyze_acceleration_signals(self, time_key, files):
        """分析加速信号数据"""
        analysis = {'signals': [], 'stats': {}}
        all_signals = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                signals = df.to_dict('records')
                all_signals.extend(signals)

        analysis['signals'] = all_signals[:20]  # 只保留前20个信号
        analysis['stats'] = {
            'total_signals': len(all_signals),
            'files_count': len(files)
        }
        return analysis

    def analyze_big_deal(self, time_key, files):
        """分析大宗交易数据"""
        analysis = {'deals': [], 'stats': {}}
        all_deals = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                deals = df.to_dict('records')
                all_deals.extend(deals)

        # 按成交额排序
        if all_deals:
            try:
                for deal in all_deals:
                    if '成交额' in deal:
                        deal['成交额'] = pd.to_numeric(deal['成交额'], errors='coerce')
                all_deals = sorted(all_deals, key=lambda x: x.get('成交额', 0), reverse=True)
            except:
                pass

        analysis['deals'] = all_deals[:10]  # 只保留前10个大宗交易
        analysis['stats'] = {
            'total_deals': len(all_deals),
            'files_count': len(files),
            'total_amount': sum(deal.get('成交额', 0) for deal in all_deals if isinstance(deal.get('成交额'), (int, float)))
        }
        return analysis

    def analyze_fund_flow_rank(self, time_key, files):
        """分析资金流向排名数据"""
        analysis = {'rankings': [], 'stats': {}}
        all_rankings = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                rankings = df.to_dict('records')
                all_rankings.extend(rankings)

        analysis['rankings'] = all_rankings[:20]  # 只保留前20个排名
        analysis['stats'] = {
            'total_items': len(all_rankings),
            'files_count': len(files)
        }
        return analysis

    def analyze_main_fund_flow(self, time_key, files):
        """分析主力资金流向数据"""
        analysis = {'flows': [], 'stats': {}}
        all_flows = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                flows = df.to_dict('records')
                all_flows.extend(flows)

        # 按净流入排序
        if all_flows:
            try:
                net_flow_cols = [col for col in all_flows[0].keys() if '净' in col and ('流入' in col or '额' in col)]
                if net_flow_cols:
                    net_col = net_flow_cols[0]
                    for flow in all_flows:
                        if net_col in flow:
                            flow[net_col] = pd.to_numeric(flow[net_col], errors='coerce')
                    all_flows = sorted(all_flows, key=lambda x: x.get(net_col, 0), reverse=True)
            except:
                pass

        analysis['flows'] = all_flows[:15]  # 只保留前15个主力资金流
        analysis['stats'] = {
            'total_flows': len(all_flows),
            'files_count': len(files)
        }
        return analysis

    def analyze_stock_notifications(self, time_key, files):
        """分析股票通知数据"""
        analysis = {'notifications': [], 'stats': {}}
        all_notifications = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                notifications = df.to_dict('records')
                all_notifications.extend(notifications)

        # 按时间排序，最新的在前
        if all_notifications:
            try:
                # 尝试按Time列排序
                for notification in all_notifications:
                    if 'Time' in notification:
                        notification['Time'] = str(notification['Time'])
                all_notifications = sorted(all_notifications,
                                         key=lambda x: x.get('Time', ''), reverse=True)
            except:
                pass

        # 统计不同类型的通知
        signal_types = {}
        error_count = 0
        for notification in all_notifications:
            signal_type = notification.get('Signal_Type', 'Unknown')
            if signal_type == 'Error':
                error_count += 1
            signal_types[signal_type] = signal_types.get(signal_type, 0) + 1

        analysis['notifications'] = all_notifications[:20]  # 只保留前20个通知
        analysis['stats'] = {
            'total_notifications': len(all_notifications),
            'files_count': len(files),
            'signal_types': signal_types,
            'error_count': error_count
        }
        return analysis

    def analyze_individual_flow(self, time_key, files):
        """分析个股资金流排名数据"""
        analysis = {'flows': [], 'stats': {}}
        all_flows = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                flows = df.to_dict('records')
                all_flows.extend(flows)

        # 按净流入排序
        if all_flows:
            try:
                net_flow_cols = [col for col in all_flows[0].keys() if '净' in col and ('流入' in col or '额' in col)]
                if net_flow_cols:
                    net_col = net_flow_cols[0]
                    for flow in all_flows:
                        if net_col in flow:
                            flow[net_col] = pd.to_numeric(flow[net_col], errors='coerce')
                    all_flows = sorted(all_flows, key=lambda x: x.get(net_col, 0), reverse=True)
            except:
                pass

        analysis['flows'] = all_flows[:20]  # 只保留前20个个股资金流
        analysis['stats'] = {
            'total_flows': len(all_flows),
            'files_count': len(files)
        }
        return analysis

    def analyze_index(self, time_key, files):
        """分析指数数据"""
        analysis = {'indices': [], 'stats': {}}
        all_indices = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                indices = df.to_dict('records')
                all_indices.extend(indices)

        analysis['indices'] = all_indices
        analysis['stats'] = {
            'total_indices': len(all_indices),
            'files_count': len(files)
        }
        return analysis

    def analyze_board_changes(self, time_key, files):
        """分析板块变化数据"""
        analysis = {'changes': [], 'stats': {}}
        all_changes = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                changes = df.to_dict('records')
                all_changes.extend(changes)

        analysis['changes'] = all_changes
        analysis['stats'] = {
            'total_changes': len(all_changes),
            'files_count': len(files)
        }
        return analysis

    def analyze_market_flow(self, time_key, files):
        """分析市场资金流数据"""
        analysis = {'flows': [], 'stats': {}}
        all_flows = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                flows = df.to_dict('records')
                all_flows.extend(flows)

        analysis['flows'] = all_flows
        analysis['stats'] = {
            'total_flows': len(all_flows),
            'files_count': len(files)
        }
        return analysis

    def analyze_industry_board(self, time_key, files):
        """分析行业板块数据"""
        analysis = {'boards': [], 'stats': {}}
        all_boards = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                boards = df.to_dict('records')
                all_boards.extend(boards)

        analysis['boards'] = all_boards[:20]  # 只保留前20个
        analysis['stats'] = {
            'total_boards': len(all_boards),
            'files_count': len(files)
        }
        return analysis

    def analyze_indicator(self, time_key, files):
        """分析指标数据"""
        analysis = {'indicators': [], 'stats': {}}
        all_indicators = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                indicators = df.to_dict('records')
                all_indicators.extend(indicators)

        analysis['indicators'] = all_indicators
        analysis['stats'] = {
            'total_indicators': len(all_indicators),
            'files_count': len(files)
        }
        return analysis

    def analyze_lhb(self, time_key, files):
        """分析龙虎榜数据"""
        analysis = {'lhb_data': [], 'stats': {}}
        all_lhb = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                lhb = df.to_dict('records')
                all_lhb.extend(lhb)

        analysis['lhb_data'] = all_lhb
        analysis['stats'] = {
            'total_lhb': len(all_lhb),
            'files_count': len(files)
        }
        return analysis

    def analyze_dzjy(self, time_key, files):
        """分析大宗交易明细数据"""
        analysis = {'dzjy_data': [], 'stats': {}}
        all_dzjy = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                dzjy = df.to_dict('records')
                all_dzjy.extend(dzjy)

        analysis['dzjy_data'] = all_dzjy
        analysis['stats'] = {
            'total_dzjy': len(all_dzjy),
            'files_count': len(files)
        }
        return analysis

    def analyze_notices(self, time_key, files):
        """分析公告数据"""
        analysis = {'notices': [], 'stats': {}}
        all_notices = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            try:
                # 处理txt文件
                if file.endswith('.txt'):
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content:
                            all_notices.append({'content': content, 'file': file})
                else:
                    # 处理csv文件
                    df = self.load_csv_safe(filepath)
                    if df is not None and not df.empty:
                        notices = df.to_dict('records')
                        all_notices.extend(notices)
            except Exception as e:
                print(f"读取公告文件失败 {file}: {e}")

        analysis['notices'] = all_notices
        analysis['stats'] = {
            'total_notices': len(all_notices),
            'files_count': len(files)
        }
        return analysis

    def analyze_stock_signals(self, time_key, files):
        """分析股票信号数据"""
        analysis = {'signals': [], 'stats': {}}
        all_signals = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                signals = df.to_dict('records')
                all_signals.extend(signals)

        analysis['signals'] = all_signals
        analysis['stats'] = {
            'total_signals': len(all_signals),
            'files_count': len(files)
        }
        return analysis

    def analyze_big_purchase(self, time_key, files):
        """分析大笔买入异动数据"""
        analysis = {'big_purchases': [], 'stats': {}}
        all_purchases = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                purchases = df.to_dict('records')
                all_purchases.extend(purchases)

        analysis['big_purchases'] = all_purchases
        analysis['stats'] = {
            'total_purchases': len(all_purchases),
            'files_count': len(files)
        }
        return analysis

    def analyze_big_buy(self, time_key, files):
        """分析大买盘异动数据"""
        analysis = {'big_buys': [], 'stats': {}}
        all_buys = []

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                buys = df.to_dict('records')
                all_buys.extend(buys)

        analysis['big_buys'] = all_buys
        analysis['stats'] = {
            'total_buys': len(all_buys),
            'files_count': len(files)
        }
        return analysis

    def compare_time_periods(self, current_analysis, previous_analysis):
        """对比两个时间段的变化"""
        changes = {}
        if 'zt_pool' in current_analysis['details'] and 'zt_pool' in previous_analysis['details']:
            changes['zt_changes'] = self.deep_compare_zt_pool(current_analysis['details']['zt_pool'], previous_analysis['details']['zt_pool'])
        if 'zb_pool' in current_analysis['details'] and 'zb_pool' in previous_analysis['details']:
            changes['zb_changes'] = self.deep_compare_zb_pool(current_analysis['details']['zb_pool'], previous_analysis['details']['zb_pool'])
        if 'movers' in current_analysis['details'] and 'movers' in previous_analysis['details']:
            changes['movers_changes'] = self.compare_movers(current_analysis['details']['movers'], previous_analysis['details']['movers'])
        if 'individual_flow' in current_analysis['details'] and 'individual_flow' in previous_analysis['details']:
            changes['individual_flow_changes'] = self.compare_individual_flow(current_analysis['details']['individual_flow'], previous_analysis['details']['individual_flow'])
        if 'concept_flow' in current_analysis['details'] and 'concept_flow' in previous_analysis['details']:
            changes['concept_flow_changes'] = self.compare_board_flow(current_analysis['details']['concept_flow'], previous_analysis['details']['concept_flow'], '概念')
        if 'sector_flow' in current_analysis['details'] and 'sector_flow' in previous_analysis['details']:
            changes['sector_flow_changes'] = self.compare_board_flow(current_analysis['details']['sector_flow'], previous_analysis['details']['sector_flow'], '行业')
        if 'industry_board' in current_analysis['details'] and 'industry_board' in previous_analysis['details']:
            changes['industry_board_changes'] = self.compare_industry_board(current_analysis['details']['industry_board'], previous_analysis['details']['industry_board'])
        if 'fund_flow_rank' in current_analysis['details'] and 'fund_flow_rank' in previous_analysis['details']:
            changes['fund_flow_rank_changes'] = self.compare_fund_flow_rank(current_analysis['details']['fund_flow_rank'], previous_analysis['details']['fund_flow_rank'])
        if 'news' in current_analysis['details'] and 'news' in previous_analysis['details']:
            changes['news_changes'] = self.compare_news(current_analysis['details']['news'], previous_analysis['details']['news'])
        return changes

    def compare_news(self, current_news, previous_news):
        """对比新闻变化"""
        changes = {}
        current_titles = set([news['title'] for news in current_news['news_list']])
        previous_titles = set([news['title'] for news in previous_news['news_list']])

        new_news = current_titles - previous_titles
        changes['new_news_count'] = len(new_news)
        changes['new_news'] = list(new_news)[:3]  # 只显示前3条新新闻

        return changes

    def compare_industry_board(self, current_board, previous_board):
        """对比行业板块汇总变化"""
        changes = {}

        # 获取行业数据映射
        def get_board_map(board_data):
            board_map = {}
            if not board_data or 'boards' not in board_data:
                return board_map

            for board in board_data['boards']:
                # 智能识别行业名称字段
                industry_name = None
                for name_key in ['行业', '板块名称', '名称', 'industry']:
                    if name_key in board:
                        industry_name = board[name_key]
                        break

                if industry_name:
                    # 获取涨跌幅或其他关键指标
                    change_pct = board.get('涨跌幅', board.get('涨跌', 0))
                    stock_count = board.get('股票数量', board.get('数量', 0))

                    board_map[industry_name] = {
                        'change_pct': change_pct,
                        'stock_count': stock_count,
                        'data': board
                    }
            return board_map

        current_map = get_board_map(current_board)
        previous_map = get_board_map(previous_board)

        if not current_map or not previous_map:
            return changes

        # 分析行业变化
        industry_changes = []
        for industry, c_data in current_map.items():
            p_data = previous_map.get(industry)
            if p_data:
                change_diff = c_data['change_pct'] - p_data['change_pct']
                if abs(change_diff) > 1:  # 变化超过1%才记录
                    industry_changes.append({
                        'industry': industry,
                        'previous_change': p_data['change_pct'],
                        'current_change': c_data['change_pct'],
                        'change_diff': change_diff,
                        'current_count': c_data['stock_count'],
                        'previous_count': p_data['stock_count']
                    })

        # 按变化幅度排序
        industry_changes.sort(key=lambda x: abs(x['change_diff']), reverse=True)
        changes['industry_changes'] = industry_changes[:5]  # 只保留前5个变化最大的

        # 新增行业
        new_industries = set(current_map.keys()) - set(previous_map.keys())
        changes['new_industries'] = list(new_industries)[:3]

        # 消失行业
        lost_industries = set(previous_map.keys()) - set(current_map.keys())
        changes['lost_industries'] = list(lost_industries)[:3]

        return changes

    def compare_fund_flow_rank(self, current_rank, previous_rank):
        """对比资金流向排名变化"""
        changes = {}

        # 获取排名映射
        def get_rank_map(rank_data):
            rank_map = {}
            if not rank_data or 'rankings' not in rank_data:
                return rank_map

            for i, ranking in enumerate(rank_data['rankings'][:20]):  # 只关注前20
                name = ranking.get('名称', ranking.get('股票简称', 'N/A'))
                net_flow = ranking.get('今日主力净流入-净额', ranking.get('净流入', 0))

                if name != 'N/A':
                    rank_map[name] = {
                        'rank': i + 1,
                        'flow': net_flow,
                        'flow_formatted': self._format_money(net_flow)
                    }
            return rank_map

        current_map = get_rank_map(current_rank)
        previous_map = get_rank_map(previous_rank)

        if not current_map or not previous_map:
            return changes

        # 排名变化分析
        ranking_changes = []
        for name, p_data in previous_map.items():
            c_data = current_map.get(name)
            if c_data and p_data['rank'] <= 10:  # 只关注前10名的变化
                rank_change = p_data['rank'] - c_data['rank']  # 正数表示排名上升
                if abs(rank_change) >= 2:  # 排名变化超过2位才记录
                    ranking_changes.append({
                        'name': name,
                        'previous_rank': p_data['rank'],
                        'current_rank': c_data['rank'],
                        'rank_change': rank_change,
                        'current_flow_formatted': c_data['flow_formatted']
                    })

        ranking_changes.sort(key=lambda x: abs(x['rank_change']), reverse=True)
        changes['ranking_changes'] = ranking_changes[:5]

        # 新进前10
        new_top10 = []
        for name, c_data in current_map.items():
            if c_data['rank'] <= 10 and name not in previous_map:
                new_top10.append({
                    'name': name,
                    'rank': c_data['rank'],
                    'flow_formatted': c_data['flow_formatted']
                })

        changes['new_top10'] = sorted(new_top10, key=lambda x: x['rank'])[:3]

        # 跌出前10
        lost_top10 = []
        for name, p_data in previous_map.items():
            if p_data['rank'] <= 10 and (name not in current_map or current_map[name]['rank'] > 10):
                lost_top10.append({
                    'name': name,
                    'previous_rank': p_data['rank'],
                    'flow_formatted': p_data['flow_formatted']
                })

        changes['lost_top10'] = sorted(lost_top10, key=lambda x: x['previous_rank'])[:3]

        return changes

    def deep_compare_zb_pool(self, current_zb, previous_zb):
        """深度对比炸板股池变化"""
        current_stocks = {stock['代码']: stock for stock in current_zb['zb_stocks']}
        previous_stocks = {stock['代码']: stock for stock in previous_zb['zb_stocks']}
        current_codes = set(current_stocks.keys())
        previous_codes = set(previous_stocks.keys())

        # 新增炸板详情
        new_zb_details = []
        for code in current_codes - previous_codes:
            stock = current_stocks[code]
            new_zb_details.append({
                'name': stock['名称'],
                'code': code,
                'industry': stock.get('所属行业', '未知'),
                'amplitude': stock.get('振幅', 0),
                'zb_count': stock.get('炸板次数', 0),
                'first_time': stock.get('首次封板时间', '')
            })

        # 失去炸板详情
        lost_zb_details = []
        for code in previous_codes - current_codes:
            stock = previous_stocks[code]
            lost_zb_details.append({
                'name': stock['名称'],
                'code': code,
                'industry': stock.get('所属行业', '未知'),
                'amplitude': stock.get('振幅', 0),
                'zb_count': stock.get('炸板次数', 0)
            })

        # 行业变化分析
        current_industries = current_zb['stats'].get('industries', {})
        previous_industries = previous_zb['stats'].get('industries', {})
        industry_changes = {}

        all_industries = set(current_industries.keys()) | set(previous_industries.keys())
        for industry in all_industries:
            current_count = current_industries.get(industry, 0)
            previous_count = previous_industries.get(industry, 0)
            if current_count != previous_count:
                change = current_count - previous_count
                change_pct = (change / previous_count * 100) if previous_count > 0 else (100 if current_count > 0 else 0)
                industry_changes[industry] = {
                    'current': current_count,
                    'previous': previous_count,
                    'change': change,
                    'change_pct': change_pct
                }

        changes = {
            'current_total': len(current_codes),
            'previous_total': len(previous_codes),
            'total_change': len(current_codes) - len(previous_codes),
            'new_zb': list(current_codes - previous_codes),
            'lost_zb': list(previous_codes - current_codes),
            'new_zb_details': new_zb_details,
            'lost_zb_details': lost_zb_details,
            'industry_changes': industry_changes
        }

        return changes

    def compare_board_flow(self, current_flow, previous_flow, board_type):
        """对比板块资金流变化 - 增强版"""
        changes = {'type': board_type}

        def get_flow_map(flow_data):
            flow_map = {}
            if not flow_data or 'top_inflow' not in flow_data:
                return flow_map

            for i, flow in enumerate(flow_data['top_inflow'][:10]):  # 只关注前10
                name = flow.get('名称', 'N/A')
                net_val = flow.get('今日主力净流入-净额', 0)
                if name != 'N/A':
                    flow_map[name] = {
                        'rank': i + 1,
                        'flow': net_val,
                        'flow_formatted': self._format_money(net_val)
                    }
            return flow_map

        current_map = get_flow_map(current_flow)
        previous_map = get_flow_map(previous_flow)

        if not current_map or not previous_map:
            return changes

        # 增强版排名变化分析
        ranking_changes = []
        for name, p_data in previous_map.items():
            c_data = current_map.get(name)
            if c_data and p_data['rank'] <= 5:  # 只关注前5名的变化
                rank_change = c_data['rank'] - p_data['rank']
                flow_change = c_data['flow'] - p_data['flow']
                if rank_change != 0:
                    ranking_changes.append({
                        'name': name,
                        'previous_rank': p_data['rank'],
                        'current_rank': c_data['rank'],
                        'rank_change': rank_change,
                        'previous_flow': p_data['flow'],
                        'current_flow': c_data['flow'],
                        'flow_change': flow_change,
                        'flow_change_formatted': self._format_money(flow_change),
                        'current_flow_formatted': c_data['flow_formatted']
                    })

        changes['ranking_changes'] = sorted(ranking_changes, key=lambda x: abs(x['rank_change']), reverse=True)[:3]

        # 增强版新进入前5的板块
        current_top5_names = [name for name, data in current_map.items() if data['rank'] <= 5]
        previous_top5_names = [name for name, data in previous_map.items() if data['rank'] <= 5]
        current_top5 = set(current_top5_names)
        previous_top5 = set(previous_top5_names)

        new_top5_details = []
        for name in current_top5 - previous_top5:
            if name in current_map:
                new_top5_details.append({
                    'name': name,
                    'rank': current_map[name]['rank'],
                    'flow': current_map[name]['flow'],
                    'flow_formatted': current_map[name]['flow_formatted']
                })

        lost_top5_details = []
        for name in previous_top5 - current_top5:
            if name in previous_map:
                lost_top5_details.append({
                    'name': name,
                    'previous_rank': previous_map[name]['rank'],
                    'flow': previous_map[name]['flow'],
                    'flow_formatted': previous_map[name]['flow_formatted']
                })

        changes['new_top5'] = [item['name'] for item in new_top5_details]
        changes['lost_top5'] = [item['name'] for item in lost_top5_details]
        changes['new_top5_details'] = new_top5_details
        changes['lost_top5_details'] = lost_top5_details

        return changes

    def deep_compare_zt_pool(self, current_zt, previous_zt):
        """深度对比涨停池变化 - 增强版"""
        current_stocks = {stock['代码']: stock for stock in current_zt['zt_stocks']}
        previous_stocks = {stock['代码']: stock for stock in previous_zt['zt_stocks']}
        current_codes = set(current_stocks.keys())
        previous_codes = set(previous_stocks.keys())

        # 增强版新增涨停详情 - 包含更多信息
        new_zt_details = []
        for code in current_codes - previous_codes:
            stock = current_stocks[code]
            new_zt_details.append({
                'name': stock['名称'],
                'code': code,
                'industry': stock.get('所属行业', '未知'),
                'board_count': stock.get('连板数', 1),
                'reason': stock.get('涨停原因', ''),
                'first_time': stock.get('首次涨停时间', '')
            })

        # 增强版失去涨停详情 - 包含更多信息
        lost_zt_details = []
        for code in previous_codes - current_codes:
            stock = previous_stocks[code]
            lost_zt_details.append({
                'name': stock['名称'],
                'code': code,
                'industry': stock.get('所属行业', '未知'),
                'board_count': stock.get('连板数', 1),
                'reason': stock.get('涨停原因', '')
            })

        # 增强版行业变化分析
        current_industries = pd.Series([s.get('所属行业', '未知') for s in current_zt['zt_stocks']]).value_counts()
        previous_industries = pd.Series([s.get('所属行业', '未知') for s in previous_zt['zt_stocks']]).value_counts()
        industry_changes = {}
        all_industries = set(current_industries.index) | set(previous_industries.index)
        for ind in all_industries:
            c_count, p_count = current_industries.get(ind, 0), previous_industries.get(ind, 0)
            if c_count != p_count:
                industry_changes[ind] = {
                    'previous': p_count,
                    'current': c_count,
                    'change': c_count - p_count,
                    'change_pct': ((c_count - p_count) / max(p_count, 1)) * 100 if p_count > 0 else 100
                }

        # 连板数分布变化
        current_boards = pd.Series([s.get('连板数', 1) for s in current_zt['zt_stocks']]).value_counts()
        previous_boards = pd.Series([s.get('连板数', 1) for s in previous_zt['zt_stocks']]).value_counts()
        board_changes = {}
        all_boards = set(current_boards.index) | set(previous_boards.index)
        for board in all_boards:
            c_count, p_count = current_boards.get(board, 0), previous_boards.get(board, 0)
            if c_count != p_count:
                board_changes[board] = {
                    'previous': p_count,
                    'current': c_count,
                    'change': c_count - p_count
                }

        return {
            'total_change': len(current_codes) - len(previous_codes),
            'new_zt_details': new_zt_details,
            'lost_zt_details': lost_zt_details,
            'industry_changes': industry_changes,
            'board_changes': board_changes,
            'current_total': len(current_codes),
            'previous_total': len(previous_codes)
        }

    def compare_movers(self, current_movers, previous_movers):
        """[核心升级] 对比板块资金流向变化(通用版)"""
        changes = {}
        tracker_key = 'sectors' if is_sector else 'concepts'

        def get_flow_map(board_data):
            flow_map = {}
            if not board_data or 'full_data' not in board_data or board_data['full_data'].empty:
                return flow_map
            df = board_data['full_data']
            # 健壮性修改：确保净流入列存在
            net_cols = [c for c in df.columns if '净' in c]
            if not net_cols: return flow_map
            net_col = net_cols[0]

            # 智能识别名称列
            name_col = None
            for col in ['名称', '行业', '板块名称', '概念名称']:
                if col in df.columns:
                    name_col = col
                    break

            if not name_col: return flow_map

            for i, row in df.iterrows():
                name = row[name_col]
                flow_map[name] = {'rank': i + 1, 'flow': row[net_col]}
            return flow_map

        current_map = get_flow_map(current_board)
        previous_map = get_flow_map(previous_board)
        if not current_map or not previous_map: return {}

        current_hot_names = set(list(current_map.keys())[:10])
        previous_hot_names = set(list(previous_map.keys())[:10])

        changes['new_hot'] = list(current_hot_names - previous_hot_names)
        changes['lost_hot'] = list(previous_hot_names - current_hot_names)

        ranking_changes, momentum_changes, new_challengers = [], [], []

        # [核心升级] 增加“异军突起”判断逻辑
        current_top3_names = list(current_map.keys())[:3]
        for name in current_top3_names:
            if name not in self.first_appearance_tracker[tracker_key]:
                new_challengers.append({
                    'name': name,
                    'flow': current_map[name]['flow']
                })
                self.first_appearance_tracker[tracker_key].add(name)  # 记入史册，不再重复提示

        for name, p_data in previous_map.items():
            c_data = current_map.get(name)
            if not c_data: continue

            if p_data['rank'] <= 10 and c_data['rank'] > p_data['rank']:
                ranking_changes.append({'name': name, 'previous': p_data['rank'], 'current': c_data['rank']})

            if p_data['flow'] > 1e7:  # 仅计算千万以上体量的板块
                # 避免除以零
                if abs(p_data['flow']) > 1e-6:
                    ratio = (c_data['flow'] - p_data['flow']) / abs(p_data['flow'])
                    if ratio < -0.1:  # 下降超10%
                        momentum_changes.append({'name': name, 'ratio': ratio})

        changes['new_challengers'] = new_challengers
        changes['ranking_changes'] = sorted(ranking_changes, key=lambda x: x['previous'])
        changes['momentum_changes'] = sorted(momentum_changes, key=lambda x: x['ratio'])
        return changes
    
    def compare_movers(self, current_movers, previous_movers):
        """对比异动股票变化"""
        # ... 此函数无重大修改 ...
        changes = {}
        current_by_type = defaultdict(list)
        for m in current_movers['movers']: current_by_type[m['type']].append(m['name'])
        
        previous_by_type = defaultdict(list)
        for m in previous_movers['movers']: previous_by_type[m['type']].append(m['name'])

        for m_type in set(current_by_type.keys()) | set(previous_by_type.keys()):
            current_set = set(current_by_type[m_type])
            previous_set = set(previous_by_type[m_type])
            new_stocks = current_set - previous_set
            lost_stocks = previous_set - current_set

            if new_stocks or lost_stocks:
                changes[m_type] = {
                    'new_count': len(new_stocks),
                    'lost_count': len(lost_stocks),
                    'new_stocks': list(new_stocks),
                    'lost_stocks': list(lost_stocks),
                    'total_change': len(current_set) - len(previous_set)
                }
        return changes

    def compare_individual_flow(self, current_flow, previous_flow):
        """对比个股资金流变化 - 增强版"""
        changes = {}

        def get_flow_map(flow_data):
            flow_map = {}
            if not flow_data or 'flows' not in flow_data:
                return flow_map
            flows = flow_data['flows']
            if not flows:
                return flow_map

            # 智能识别净流入列和名称列
            net_cols = [k for k in flows[0].keys() if '净' in k and ('流入' in k or '额' in k)]
            name_cols = [k for k in flows[0].keys() if k in ['名称', '股票简称', '代码']]

            if not net_cols or not name_cols:
                return flow_map

            net_col = net_cols[0]
            name_col = name_cols[0]

            for i, flow in enumerate(flows):
                name = flow.get(name_col)
                if name:
                    flow_map[name] = {
                        'rank': i + 1,
                        'flow': flow.get(net_col, 0),
                        'flow_formatted': self._format_money(flow.get(net_col, 0))
                    }
            return flow_map

        current_map = get_flow_map(current_flow)
        previous_map = get_flow_map(previous_flow)

        if not current_map or not previous_map:
            return changes

        # 增强版排名变化分析
        ranking_changes = []
        for name, p_data in previous_map.items():
            c_data = current_map.get(name)
            if c_data and p_data['rank'] <= 10:  # 只关注前10名的变化
                rank_change = c_data['rank'] - p_data['rank']
                flow_change = c_data['flow'] - p_data['flow']
                if rank_change != 0:
                    ranking_changes.append({
                        'name': name,
                        'previous_rank': p_data['rank'],
                        'current_rank': c_data['rank'],
                        'rank_change': rank_change,
                        'previous_flow': p_data['flow'],
                        'current_flow': c_data['flow'],
                        'flow_change': flow_change,
                        'flow_change_formatted': self._format_money(flow_change),
                        'current_flow_formatted': c_data['flow_formatted']
                    })

        changes['ranking_changes'] = sorted(ranking_changes, key=lambda x: abs(x['rank_change']), reverse=True)[:5]

        # 增强版新进入前10的股票
        current_top10_names = list(current_map.keys())[:10]
        previous_top10_names = list(previous_map.keys())[:10]
        current_top10 = set(current_top10_names)
        previous_top10 = set(previous_top10_names)

        new_top10_details = []
        for name in current_top10 - previous_top10:
            if name in current_map:
                new_top10_details.append({
                    'name': name,
                    'rank': current_map[name]['rank'],
                    'flow': current_map[name]['flow'],
                    'flow_formatted': current_map[name]['flow_formatted']
                })

        lost_top10_details = []
        for name in previous_top10 - current_top10:
            if name in previous_map:
                lost_top10_details.append({
                    'name': name,
                    'previous_rank': previous_map[name]['rank'],
                    'flow': previous_map[name]['flow'],
                    'flow_formatted': previous_map[name]['flow_formatted']
                })

        changes['new_top10'] = [item['name'] for item in new_top10_details]
        changes['lost_top10'] = [item['name'] for item in lost_top10_details]
        changes['new_top10_details'] = new_top10_details
        changes['lost_top10_details'] = lost_top10_details

        return changes

    def _format_money(self, amount):
        """格式化金额显示"""
        if pd.isna(amount) or amount == 0:
            return "0"

        # 处理字符串类型的数据
        if isinstance(amount, str):
            try:
                amount = float(amount)
            except (ValueError, TypeError):
                return str(amount)  # 如果无法转换，直接返回原字符串

        if abs(amount) >= 1e8:
            return f"{amount/1e8:.2f}亿"
        elif abs(amount) >= 1e4:
            return f"{amount/1e4:.0f}万"
        else:
            return f"{amount:.0f}"

    def generate_time_summary(self, time_key, analysis, previous_analysis=None):
        """生成时间段摘要"""
        summary = f"\n{'=' * 60}\n"
        summary += f"时间段: {time_key}\n"
        summary += f"{'=' * 60}\n"

        # 涨停池摘要
        if 'zt_pool' in analysis['details'] and analysis['details']['zt_pool']['stats']:
            zt_data = analysis['details']['zt_pool']
            summary += f"\n📈 涨停池情况:\n"
            summary += f"  - 涨停股票数量: {zt_data['stats']['total_zt']}只\n"
            if zt_data['stats']['industries']:
                top_ind = max(zt_data['stats']['industries'].items(), key=lambda x: x[1])
                summary += f"  - 热门行业: {top_ind[0]} ({top_ind[1]}只)\n"
            if zt_data['zt_stocks'] and '代码' in zt_data['zt_stocks'][0]:
                summary += f"  - 涨停股票列表:\n"
                for i, stock in enumerate(zt_data['zt_stocks'], 1):
                    # 获取连板信息
                    board_info = ""
                    if stock.get('连板数', 1) > 1:
                        board_info = f", {stock['连板数']}板"

                    # 获取行业信息
                    industry_info = ""
                    if stock.get('所属行业'):
                        industry_info = f", {stock['所属行业']}"

                    summary += f"    {i:2d}. {stock['名称']}({stock['代码']}{industry_info}{board_info})\n"

        # 炸板股池摘要 - 如果是"全天数据"，使用15点后的收盘数据
        if time_key == "全天数据":
            # 使用15点后的收盘数据，包含股票列表
            eod_zb_summary = self._get_eod_zb_pool_summary(include_list=True)
            if eod_zb_summary:
                summary += eod_zb_summary
        elif 'zb_pool' in analysis['details'] and analysis['details']['zb_pool']['stats']:
            zb_data = analysis['details']['zb_pool']
            summary += f"\n💥 炸板股池情况:\n"
            summary += f"  - 炸板股票数量: {zb_data['stats']['total_zb']}只\n"
            if zb_data['stats']['industries']:
                top_ind = max(zb_data['stats']['industries'].items(), key=lambda x: x[1])
                summary += f"  - 热门行业: {top_ind[0]} ({top_ind[1]}只)\n"
            if zb_data['stats'].get('avg_amplitude', 0) > 0:
                summary += f"  - 平均振幅: {zb_data['stats']['avg_amplitude']:.1f}%\n"
            if zb_data['zb_stocks'] and '代码' in zb_data['zb_stocks'][0]:
                summary += f"  - 炸板股票列表:\n"
                for i, stock in enumerate(zb_data['zb_stocks'], 1):
                    # 获取振幅信息
                    amplitude_info = ""
                    if stock.get('振幅', 0) > 0:
                        amplitude_info = f", 振幅{stock['振幅']:.1f}%"

                    # 获取炸板次数信息
                    zb_info = ""
                    if stock.get('炸板次数', 0) > 0:
                        zb_info = f", {stock['炸板次数']}次炸板"

                    # 获取行业信息
                    industry_info = ""
                    if stock.get('所属行业'):
                        industry_info = f", {stock['所属行业']}"

                    summary += f"    {i:2d}. {stock['名称']}({stock['代码']}{industry_info}{amplitude_info}{zb_info})\n"

        # 新闻摘要
        if 'news' in analysis['details'] and analysis['details']['news']['news_list']:
            summary += f"\n📰 重要新闻:\n"
            for news in analysis['details']['news']['news_list'][:3]:
                summary += f"  - {news['title'][:50]}...\n"

        # 异动股票
        if 'movers' in analysis['details'] and analysis['details']['movers']['movers']:
            summary += f"\n🚀 异动股票:\n"
            mover_types = defaultdict(list)
            for m in analysis['details']['movers']['movers']: mover_types[m['type']].append(m['name'])
            for m_type, stocks in mover_types.items():
                summary += f"  - {m_type}: {len(stocks)}只 ({', '.join(stocks[:3])}...)\n"

        # 板块异动
        if 'board_changes' in analysis['details'] and analysis['details']['board_changes']['changes']:
            summary += f"\n📊 板块异动:\n"
            changes = analysis['details']['board_changes']['changes']
            summary += f"  - 异动板块数量: {len(changes)}个\n"
            if changes:
                # 按主力净流入排序，显示前3个
                sorted_changes = sorted(changes, key=lambda x: float(x.get('主力净流入', 0)), reverse=True)
                top_changes = sorted_changes[:3]
                change_texts = []
                for change in top_changes:
                    name = change.get('板块名称', 'N/A')
                    net_inflow = float(change.get('主力净流入', 0))
                    change_count = change.get('板块异动总次数', 0)
                    change_texts.append(f"{name}({net_inflow:.0f}万, {change_count}次)")
                summary += f"  - 资金流入前3: {', '.join(change_texts)}\n"

        # 行业板块汇总
        if 'sector_summary' in analysis['details'] and analysis['details']['sector_summary']['sector_summaries']:
            summary += f"\n🏭 行业板块汇总:\n"
            sector_data = analysis['details']['sector_summary']
            summary += f"  - 涉及行业: {sector_data['stats']['total_sectors']}个\n"

            # 显示资金流入最多的行业
            top_sectors = []
            for sector in sector_data['sector_summaries'][:3]:
                sector_name = sector['sector']
                # 计算该行业的总资金流入
                total_inflow = 0
                positive_count = 0
                for stock in sector.get('stocks', []):
                    net_flow = stock.get('今日主力净流入-净额', 0)
                    if isinstance(net_flow, (int, float)) and net_flow > 0:
                        total_inflow += net_flow
                        positive_count += 1

                if total_inflow > 0:
                    inflow_wan = total_inflow / 10000
                    top_sectors.append(f"{sector_name}({inflow_wan:.0f}万)")
                else:
                    # 如果没有正向资金流入，显示股票数量
                    top_sectors.append(f"{sector_name}({sector['count']}只)")

            if top_sectors:
                summary += f"  - 资金流入前3: {', '.join(top_sectors)}\n"

        # 概念板块资金流
        if 'concept_flow' in analysis['details'] and analysis['details']['concept_flow'].get('top_inflow'):
            summary += f"\n💡 概念板块资金流:\n"
            flow_data = analysis['details']['concept_flow']
            top_inflows = flow_data['top_inflow'][:3]
            if top_inflows:
                flow_texts = []
                for flow in top_inflows:
                    # 智能识别名称字段
                    name = 'N/A'
                    for name_key in ['名称', '行业', '板块名称', '概念名称']:
                        if name_key in flow:
                            name = flow[name_key]
                            break

                    # 智能识别净流入字段
                    net_val = 0
                    for net_key in ['今日主力净流入-净额', '净额', '净流入']:
                        if net_key in flow:
                            net_val = flow[net_key]
                            break

                    if isinstance(net_val, (int, float)) and net_val != 0:
                        # 智能判断单位：如果数值较小，可能已经是万元单位
                        if abs(net_val) < 1000:  # 小于1000，可能已经是万元
                            flow_texts.append(f"{name}({net_val:.0f}万)")
                        else:  # 大于1000，可能是元，需要转换为万元
                            net_val_wan = net_val / 10000
                            flow_texts.append(f"{name}({net_val_wan:.0f}万)")
                    else:
                        flow_texts.append(name)
                summary += f"  - 资金流入前3: {', '.join(flow_texts)}\n"

        # 行业板块资金流
        if 'sector_flow' in analysis['details'] and analysis['details']['sector_flow']['top_inflow']:
            summary += f"\n🏭 行业板块资金流:\n"
            flow_data = analysis['details']['sector_flow']
            top_inflows = flow_data['top_inflow'][:3]
            if top_inflows:
                flow_texts = []
                for flow in top_inflows:
                    # 智能识别名称字段
                    name = 'N/A'
                    for name_key in ['名称', '行业', '板块名称', '概念名称']:
                        if name_key in flow:
                            name = flow[name_key]
                            break

                    # 智能识别净流入字段
                    net_val = 0
                    for net_key in ['今日主力净流入-净额', '净额', '净流入']:
                        if net_key in flow:
                            net_val = flow[net_key]
                            break

                    if isinstance(net_val, (int, float)) and net_val != 0:
                        # 智能判断单位：如果数值较小，可能已经是万元单位
                        if abs(net_val) < 1000:  # 小于1000，可能已经是万元
                            flow_texts.append(f"{name}({net_val:.0f}万)")
                        else:  # 大于1000，可能是元，需要转换为万元
                            net_val_wan = net_val / 10000
                            flow_texts.append(f"{name}({net_val_wan:.0f}万)")
                    else:
                        flow_texts.append(name)
                summary += f"  - 资金流入前3: {', '.join(flow_texts)}\n"

        # 加速信号
        if 'acceleration_signals' in analysis['details'] and analysis['details']['acceleration_signals']['signals']:
            summary += f"\n⚡ 加速信号:\n"
            signal_data = analysis['details']['acceleration_signals']
            summary += f"  - 信号总数: {signal_data['stats']['total_signals']}个\n"
            if signal_data['signals']:
                summary += f"  - 代表信号: {', '.join([str(s.get('名称', s.get('代码', 'N/A'))) for s in signal_data['signals'][:3]])}\n"

        # 大宗交易
        if 'big_deal' in analysis['details'] and analysis['details']['big_deal']['deals']:
            summary += f"\n💰 大宗交易:\n"
            deal_data = analysis['details']['big_deal']
            summary += f"  - 交易笔数: {deal_data['stats']['total_deals']}笔\n"
            if deal_data['stats']['total_amount'] > 0:
                summary += f"  - 总成交额: {deal_data['stats']['total_amount']:.0f}万元\n"
            if deal_data['deals']:
                top_deals = [f"{d.get('股票简称', 'N/A')}({d.get('成交额', 0):.0f}万)" for d in deal_data['deals'][:3]]
                summary += f"  - 主要交易: {', '.join(top_deals)}\n"

        # 资金流向排名
        if 'fund_flow_rank' in analysis['details'] and analysis['details']['fund_flow_rank']['rankings']:
            summary += f"\n📊 资金流向排名:\n"
            rank_data = analysis['details']['fund_flow_rank']
            summary += f"  - 排名项目: {rank_data['stats']['total_items']}个\n"
            if rank_data['rankings']:
                top_rankings = []
                for ranking in rank_data['rankings'][:3]:
                    name = ranking.get('名称', ranking.get('股票简称', 'N/A'))
                    net_flow = ranking.get('今日主力净流入-净额', ranking.get('净流入', 0))
                    if isinstance(net_flow, (int, float)) and net_flow != 0:
                        net_flow_wan = net_flow / 10000 if net_flow > 10000 else net_flow
                        top_rankings.append(f"{name}({net_flow_wan:.0f}万)")
                    else:
                        top_rankings.append(name)
                if top_rankings:
                    summary += f"  - 资金流入前3: {', '.join(top_rankings)}\n"



        # 个股资金流向 (fund_flow类型)
        if 'fund_flow' in analysis['details'] and analysis['details']['fund_flow']['top_inflow']:
            summary += f"\n💰 个股资金流向:\n"
            fund_flow_data = analysis['details']['fund_flow']
            top_inflows = fund_flow_data['top_inflow'][:3]
            if top_inflows:
                flow_texts = []
                for flow in top_inflows:
                    # 智能识别名称字段
                    name = 'N/A'
                    for name_key in ['名称', 'name', '股票简称', '代码', 'code']:
                        if name_key in flow:
                            name = flow[name_key]
                            break

                    # 智能识别净流入字段
                    net_val = 0
                    for net_key in ['main_net_inflow', '今日主力净流入-净额', '净额', '净流入', '主力净流入']:
                        if net_key in flow:
                            net_val = flow[net_key]
                            break

                    if isinstance(net_val, (int, float)) and net_val != 0:
                        # 智能判断单位：如果数值较大，可能是元，需要转换为万元
                        if abs(net_val) > 10000:  # 大于1万，可能是元，转换为万元
                            net_val_wan = net_val / 10000
                            flow_texts.append(f"{name}({net_val_wan:.0f}万)")
                        else:  # 小于1万，可能已经是万元
                            flow_texts.append(f"{name}({net_val:.0f}万)")
                    else:
                        flow_texts.append(name)
                summary += f"  - 资金流入前3: {', '.join(flow_texts)}\n"

        # 个股资金流排名 (individual_flow类型)
        if 'individual_flow' in analysis['details'] and analysis['details']['individual_flow']['flows']:
            summary += f"\n📈 个股资金流排名:\n"
            flow_data = analysis['details']['individual_flow']
            summary += f"  - 排名股票: {flow_data['stats']['total_flows']}只\n"
            if flow_data['flows']:
                # 智能识别名称和净流入列
                top_flows = []
                for flow in flow_data['flows'][:3]:
                    name_cols = [k for k in flow.keys() if k in ['名称', '股票简称', '代码']]
                    net_cols = [k for k in flow.keys() if '净' in k and ('流入' in k or '额' in k)]
                    if name_cols and net_cols:
                        name = flow.get(name_cols[0], 'N/A')
                        net_val = flow.get(net_cols[0], 0)
                        if isinstance(net_val, (int, float)) and net_val != 0:
                            top_flows.append(f"{name}({net_val:.0f}万)")
                        else:
                            top_flows.append(name)
                    else:
                        name_cols = [k for k in flow.keys() if k in ['名称', '股票简称', '代码']]
                        if name_cols:
                            top_flows.append(flow.get(name_cols[0], 'N/A'))
                if top_flows:
                    summary += f"  - 资金流入前3: {', '.join(top_flows)}\n"

        # 指数数据
        if 'index' in analysis['details'] and analysis['details']['index']['indices']:
            summary += f"\n📊 指数数据:\n"
            index_data = analysis['details']['index']
            summary += f"  - 指数数量: {index_data['stats']['total_indices']}个\n"

        # 龙虎榜
        if 'lhb' in analysis['details'] and analysis['details']['lhb']['lhb_data']:
            summary += f"\n🐉 龙虎榜:\n"
            lhb_data = analysis['details']['lhb']
            summary += f"  - 上榜股票: {lhb_data['stats']['total_lhb']}只\n"

        # 公告
        if 'notices' in analysis['details'] and analysis['details']['notices']['notices']:
            summary += f"\n📋 公告:\n"
            notice_data = analysis['details']['notices']
            summary += f"  - 公告数量: {notice_data['stats']['total_notices']}条\n"

        # 大宗交易
        if 'big_deal' in analysis['details'] and analysis['details']['big_deal']['deals']:
            summary += f"\n💰 大宗交易:\n"
            deal_data = analysis['details']['big_deal']
            summary += f"  - 交易笔数: {deal_data['stats']['total_deals']}笔\n"
            if deal_data['stats'].get('total_amount', 0) > 0:
                summary += f"  - 总成交额: {deal_data['stats']['total_amount']:.0f}万元\n"
            if deal_data['deals']:
                top_deals = []
                for deal in deal_data['deals'][:3]:
                    name = deal.get('股票简称', deal.get('名称', 'N/A'))
                    amount = deal.get('成交额', 0)
                    if isinstance(amount, (int, float)) and amount > 0:
                        top_deals.append(f"{name}({amount:.0f}万)")
                    else:
                        top_deals.append(name)
                if top_deals:
                    summary += f"  - 主要交易: {', '.join(top_deals)}\n"

        # 指标数据
        if 'indicator' in analysis['details'] and analysis['details']['indicator']['indicators']:
            summary += f"\n📈 技术指标:\n"
            indicator_data = analysis['details']['indicator']
            summary += f"  - 指标股票: {indicator_data['stats']['total_indicators']}只\n"
            if indicator_data['indicators']:
                top_indicators = []
                for indicator in indicator_data['indicators'][:3]:
                    name = indicator.get('股票简称', indicator.get('名称', 'N/A'))
                    top_indicators.append(name)
                if top_indicators:
                    summary += f"  - 创月新高: {', '.join(top_indicators)}\n"

        # 股票信号
        if 'stock_signals' in analysis['details'] and analysis['details']['stock_signals']['signals']:
            summary += f"\n📊 股票信号:\n"
            signal_data = analysis['details']['stock_signals']
            summary += f"  - 信号总数: {signal_data['stats']['total_signals']}个\n"
            if signal_data['signals']:
                top_signals = []
                for signal in signal_data['signals'][:3]:
                    name = signal.get('股票简称', signal.get('名称', 'N/A'))
                    signal_type = signal.get('信号类型', signal.get('类型', 'N/A'))
                    top_signals.append(f"{name}({signal_type})")
                if top_signals:
                    summary += f"  - 主要信号: {', '.join(top_signals)}\n"

        # 股票通知
        if 'stock_notifications' in analysis['details'] and analysis['details']['stock_notifications']['notifications']:
            summary += f"\n📱 股票通知:\n"
            notif_data = analysis['details']['stock_notifications']
            summary += f"  - 通知总数: {notif_data['stats']['total_notifications']}条\n"
            if notif_data['stats']['error_count'] > 0:
                summary += f"  - 错误通知: {notif_data['stats']['error_count']}条\n"

            # 显示主要信号类型
            signal_types = notif_data['stats']['signal_types']
            if signal_types:
                main_signals = sorted(signal_types.items(), key=lambda x: x[1], reverse=True)[:3]
                signal_texts = [f"{sig_type}({count})" for sig_type, count in main_signals if sig_type != 'Error']
                if signal_texts:
                    summary += f"  - 主要信号: {', '.join(signal_texts)}\n"

        # 板块资金流向
        def format_board_flow(board_data, board_type):
            res = ""
            if board_data and board_data['top_inflow']:
                title = '概念' if board_type == 'concept' else '行业'
                emoji = '🎯' if board_type == 'concept' else '🏭'
                res += f"\n{emoji} {title}板块资金流向:\n"
                res += f"  - 资金流入前3: "
                for concept in board_data['top_inflow'][:3]:
                    net_key = [k for k in concept.keys() if '净' in k]
                    if not net_key: continue
                    net_key = net_key[0]
                    flow_val = concept.get(net_key, 0)
                    # 智能判断单位：如果数值较小，可能已经是万元单位
                    if abs(flow_val) < 1000:  # 小于1000，可能已经是万元
                        flow_amount = f"({flow_val:.0f}万)" if flow_val else ""
                    else:  # 大于1000，可能是元，需要转换为万元
                        flow_amount = f"({flow_val / 10000:.0f}万)" if flow_val else ""

                    # 智能识别名称
                    name = concept.get('名称', concept.get('行业', concept.get('板块名称', concept.get('概念名称', ''))))
                    res += f"{name}{flow_amount} "
                res += "\n"
            return res

        if 'concept_flow' in analysis['details']: summary += format_board_flow(analysis['details']['concept_flow'],
                                                                               'concept')
        if 'sector_flow' in analysis['details']: summary += format_board_flow(analysis['details']['sector_flow'],
                                                                              'sector')

        # 深度变化对比
        if previous_analysis:
            changes = self.compare_time_periods(analysis, previous_analysis)
            if changes:
                summary += f"\n🔄 深度变化分析:\n"
                if 'zt_changes' in changes:
                    ztc = changes['zt_changes']
                    summary += f"  📈 涨停池变化:\n"
                    summary += f"    - 总体变化: {ztc['total_change']:+d}只 ({ztc['previous_total']}只 → {ztc['current_total']}只)\n"

                    if ztc['new_zt_details']:
                        new_zt_texts = []
                        for s in ztc['new_zt_details']:
                            if s["board_count"] > 1:
                                new_zt_texts.append(f'{s["name"]}({s["code"]}, {s["industry"]}, {s["board_count"]}板)')
                            else:
                                new_zt_texts.append(f'{s["name"]}({s["code"]}, {s["industry"]})')

                        if len(new_zt_texts) <= 3:
                            summary += f"    ✅ 新增涨停({len(ztc['new_zt_details'])}只):\n"
                            for text in new_zt_texts:
                                summary += f"      - {text}\n"
                        else:
                            summary += f"    ✅ 新增涨停({len(ztc['new_zt_details'])}只):\n"
                            for text in new_zt_texts[:3]:
                                summary += f"      - {text}\n"
                            summary += f"      - ... 还有{len(new_zt_texts)-3}只\n"

                    if ztc['lost_zt_details']:
                        lost_zt_texts = []
                        for s in ztc['lost_zt_details']:
                            if s["board_count"] > 1:
                                lost_zt_texts.append(f'{s["name"]}({s["code"]}, {s["industry"]}, {s["board_count"]}板)')
                            else:
                                lost_zt_texts.append(f'{s["name"]}({s["code"]}, {s["industry"]})')

                        if len(lost_zt_texts) <= 3:
                            summary += f"    ❌ 失去涨停({len(ztc['lost_zt_details'])}只):\n"
                            for text in lost_zt_texts:
                                summary += f"      - {text}\n"
                        else:
                            summary += f"    ❌ 失去涨停({len(ztc['lost_zt_details'])}只):\n"
                            for text in lost_zt_texts[:3]:
                                summary += f"      - {text}\n"
                            summary += f"      - ... 还有{len(lost_zt_texts)-3}只\n"

                    if ztc['industry_changes']:
                        summary += f"    🏭 行业变化:\n"
                        for ind, v in list(ztc['industry_changes'].items())[:3]:
                            change_pct = v.get('change_pct', 0)
                            summary += f"      - {ind}: {v['previous']}只 → {v['current']}只 ({v['change']:+d}, {change_pct:+.1f}%)\n"

                    if ztc.get('board_changes'):
                        summary += f"    📊 连板分布变化:\n"
                        for board, v in list(ztc['board_changes'].items())[:3]:
                            summary += f"      - {board}: {v['previous']}只 → {v['current']}只 ({v['change']:+d})\n"

                # 炸板股池变化
                if 'zb_changes' in changes:
                    zbc = changes['zb_changes']
                    summary += f"  💥 炸板股池变化:\n"
                    summary += f"    - 总体变化: {zbc['total_change']:+d}只 ({zbc['previous_total']}只 → {zbc['current_total']}只)\n"

                    if zbc['new_zb_details']:
                        new_zb_texts = []
                        for s in zbc['new_zb_details']:
                            amplitude_info = f", 振幅{s['amplitude']:.1f}%" if s['amplitude'] > 0 else ""
                            zb_info = f", {s['zb_count']}次炸板" if s['zb_count'] > 0 else ""
                            new_zb_texts.append(f'{s["name"]}({s["code"]}, {s["industry"]}{amplitude_info}{zb_info})')

                        if len(new_zb_texts) <= 3:
                            summary += f"    ✅ 新增炸板({len(zbc['new_zb_details'])}只):\n"
                            for text in new_zb_texts:
                                summary += f"      - {text}\n"
                        else:
                            summary += f"    ✅ 新增炸板({len(zbc['new_zb_details'])}只):\n"
                            for text in new_zb_texts[:3]:
                                summary += f"      - {text}\n"
                            summary += f"      - ... 还有{len(new_zb_texts)-3}只\n"

                    if zbc['lost_zb_details']:
                        lost_zb_texts = []
                        for s in zbc['lost_zb_details']:
                            amplitude_info = f", 振幅{s['amplitude']:.1f}%" if s['amplitude'] > 0 else ""
                            zb_info = f", {s['zb_count']}次炸板" if s['zb_count'] > 0 else ""
                            lost_zb_texts.append(f'{s["name"]}({s["code"]}, {s["industry"]}{amplitude_info}{zb_info})')

                        if len(lost_zb_texts) <= 3:
                            summary += f"    ❌ 失去炸板({len(zbc['lost_zb_details'])}只):\n"
                            for text in lost_zb_texts:
                                summary += f"      - {text}\n"
                        else:
                            summary += f"    ❌ 失去炸板({len(zbc['lost_zb_details'])}只):\n"
                            for text in lost_zb_texts[:3]:
                                summary += f"      - {text}\n"
                            summary += f"      - ... 还有{len(lost_zb_texts)-3}只\n"

                    if zbc['industry_changes']:
                        summary += f"    🏭 炸板行业变化:\n"
                        for ind, v in list(zbc['industry_changes'].items())[:3]:
                            change_pct = v.get('change_pct', 0)
                            summary += f"      - {ind}: {v['previous']}只 → {v['current']}只 ({v['change']:+d}, {change_pct:+.1f}%)\n"

                # 异动股票变化
                if 'movers_changes' in changes:
                    mc = changes['movers_changes']
                    summary += f"  🚀 异动股票变化:\n"
                    for mover_type, change_data in mc.items():
                        if change_data['new_count'] > 0 or change_data['lost_count'] > 0:
                            summary += f"    - {mover_type}: "
                            if change_data['new_count'] > 0:
                                new_stocks = change_data['new_stocks'][:3]  # 显示前3只
                                summary += f"新增{change_data['new_count']}只({', '.join(new_stocks)})"
                            if change_data['lost_count'] > 0:
                                if change_data['new_count'] > 0:
                                    summary += f", "
                                lost_stocks = change_data['lost_stocks'][:3]  # 显示前3只
                                summary += f"失去{change_data['lost_count']}只({', '.join(lost_stocks)})"
                            summary += f"\n"

                # 新闻变化
                if 'news_changes' in changes:
                    nc = changes['news_changes']
                    if nc['new_news_count'] > 0:
                        summary += f"  📰 新闻变化:\n"
                        summary += f"    - 新增新闻: {nc['new_news_count']}条\n"
                        if nc['new_news']:
                            for news in nc['new_news']:
                                # 截断过长的新闻标题
                                title = news[:50] + "..." if len(news) > 50 else news
                                summary += f"      • {title}\n"

                def format_board_changes(board_changes, board_type):
                    res = ""
                    title = '概念' if board_type == 'concept' else '行业'
                    emoji = '🎯' if board_type == 'concept' else '🏭'

                    has_changes = any(board_changes.get(key) for key in
                                      ['new_challengers', 'ranking_changes', 'momentum_changes', 'new_hot', 'lost_hot'])

                    if board_changes and has_changes:
                        res += f"  {emoji} {title}板块变化:\n"
                        # [核心升级] 新增“异军突起”的格式化输出
                        if board_changes.get('new_challengers'):
                            challenger = board_changes['new_challengers'][0]
                            flow_val = challenger['flow']
                            flow_amount = f"({flow_val / 1e8:.2f}亿)" if abs(
                                flow_val) >= 1e8 else f"({flow_val / 1e4:.0f}万)"
                            res += f"    🚀 **异军突起**: {challenger['name']} 首次进入前三 {flow_amount}!\n"
                        if board_changes.get('ranking_changes'):
                            ranking_texts = [f'{i["name"]}({i["previous"]}→{i["current"]})' for i in
                                             board_changes['ranking_changes'][:2]]
                            res += f"    - 排名下降: {' '.join(ranking_texts)}\n"
                        if board_changes.get('momentum_changes'):
                            momentum_texts = [f'{i["name"]}({i["ratio"]:.0%})' for i in
                                              board_changes['momentum_changes'][:2]]
                            res += f"    - 动能减弱: {' '.join(momentum_texts)}\n"
                        if board_changes.get('new_hot'):
                            res += f"    - 新进热门概念: {', '.join(board_changes['new_hot'][:3])}\n"
                        if board_changes.get('lost_hot'):
                            res += f"    - 退出热门概念: {', '.join(board_changes['lost_hot'][:3])}\n"
                    return res

                if 'concept_changes' in changes: summary += format_board_changes(changes['concept_changes'], 'concept')
                if 'sector_changes' in changes: summary += format_board_changes(changes['sector_changes'], 'sector')

                if 'movers_changes' in changes and changes['movers_changes']:
                    summary += f"  🚀 异动股票变化:\n"
                    for m_type, chg in list(changes['movers_changes'].items())[:2]:
                        summary += f"    - {m_type}: 新增{chg['new_count']}只\n"

                if 'individual_flow_changes' in changes and changes['individual_flow_changes']:
                    ifc = changes['individual_flow_changes']
                    summary += f"  💰 个股资金流变化:\n"
                    if ifc.get('ranking_changes'):
                        summary += f"    📈 排名变化:\n"
                        for r in ifc['ranking_changes'][:3]:
                            direction = "↗️" if r['rank_change'] < 0 else "↘️"
                            summary += f"      {direction} {r['name']}: {r['previous_rank']}位 → {r['current_rank']}位 | {r['current_flow_formatted']}\n"

                    if ifc.get('new_top10_details'):
                        summary += f"    ✅ 新进前10:\n"
                        for detail in ifc['new_top10_details'][:3]:
                            summary += f"      - {detail['name']}: 第{detail['rank']}位 | {detail['flow_formatted']}\n"

                    if ifc.get('lost_top10_details'):
                        summary += f"    ❌ 跌出前10:\n"
                        for detail in ifc['lost_top10_details'][:3]:
                            summary += f"      - {detail['name']}: 原第{detail['previous_rank']}位 | {detail['flow_formatted']}\n"

                if 'concept_flow_changes' in changes and changes['concept_flow_changes']:
                    cfc = changes['concept_flow_changes']
                    summary += f"  💡 概念板块资金流变化:\n"
                    if cfc.get('ranking_changes'):
                        summary += f"    📈 排名变化:\n"
                        for r in cfc['ranking_changes'][:3]:
                            direction = "↗️" if r['rank_change'] < 0 else "↘️"
                            prev_rank = r.get('previous_rank', r.get('previous', 'N/A'))
                            curr_rank = r.get('current_rank', r.get('current', 'N/A'))
                            flow_info = r.get('current_flow_formatted', '')
                            summary += f"      {direction} {r['name']}: {prev_rank}位 → {curr_rank}位 | {flow_info}\n"

                    if cfc.get('new_top5_details'):
                        summary += f"    ✅ 新进前5:\n"
                        for detail in cfc['new_top5_details'][:3]:
                            summary += f"      - {detail['name']}: 第{detail['rank']}位 | {detail['flow_formatted']}\n"
                    elif cfc.get('new_top5'):
                        summary += f"    ✅ 新进前5: {', '.join(cfc['new_top5'][:3])}\n"

                    if cfc.get('lost_top5_details'):
                        summary += f"    ❌ 跌出前5:\n"
                        for detail in cfc['lost_top5_details'][:3]:
                            summary += f"      - {detail['name']}: 原第{detail['previous_rank']}位 | {detail['flow_formatted']}\n"
                    elif cfc.get('lost_top5'):
                        summary += f"    ❌ 跌出前5: {', '.join(cfc['lost_top5'][:3])}\n"

                if 'sector_flow_changes' in changes and changes['sector_flow_changes']:
                    sfc = changes['sector_flow_changes']
                    summary += f"  🏭 行业板块资金流变化:\n"
                    if sfc.get('ranking_changes'):
                        summary += f"    📈 排名变化:\n"
                        for r in sfc['ranking_changes'][:3]:
                            direction = "↗️" if r['rank_change'] < 0 else "↘️"
                            prev_rank = r.get('previous_rank', r.get('previous', 'N/A'))
                            curr_rank = r.get('current_rank', r.get('current', 'N/A'))
                            flow_info = r.get('current_flow_formatted', '')
                            summary += f"      {direction} {r['name']}: {prev_rank}位 → {curr_rank}位 | {flow_info}\n"

                    if sfc.get('new_top5_details'):
                        summary += f"    ✅ 新进前5:\n"
                        for detail in sfc['new_top5_details'][:3]:
                            summary += f"      - {detail['name']}: 第{detail['rank']}位 | {detail['flow_formatted']}\n"
                    elif sfc.get('new_top5'):
                        summary += f"    ✅ 新进前5: {', '.join(sfc['new_top5'][:3])}\n"

                    if sfc.get('lost_top5_details'):
                        summary += f"    ❌ 跌出前5:\n"
                        for detail in sfc['lost_top5_details'][:3]:
                            summary += f"      - {detail['name']}: 原第{detail['previous_rank']}位 | {detail['flow_formatted']}\n"
                    elif sfc.get('lost_top5'):
                        summary += f"    ❌ 跌出前5: {', '.join(sfc['lost_top5'][:3])}\n"

                # 行业板块汇总变化
                if 'industry_board_changes' in changes and changes['industry_board_changes']:
                    ibc = changes['industry_board_changes']
                    summary += f"  🏭 行业板块汇总变化:\n"

                    if ibc.get('industry_changes'):
                        summary += f"    📊 行业涨跌幅变化:\n"
                        for change in ibc['industry_changes'][:3]:
                            direction = "📈" if change['change_diff'] > 0 else "📉"
                            summary += f"      {direction} {change['industry']}: {change['previous_change']:.1f}% → {change['current_change']:.1f}% "
                            summary += f"(变化{change['change_diff']:+.1f}%)\n"

                    if ibc.get('new_industries'):
                        summary += f"    ✅ 新增行业: {', '.join(ibc['new_industries'])}\n"

                    if ibc.get('lost_industries'):
                        summary += f"    ❌ 消失行业: {', '.join(ibc['lost_industries'])}\n"

                # 资金流向排名变化
                if 'fund_flow_rank_changes' in changes and changes['fund_flow_rank_changes']:
                    frc = changes['fund_flow_rank_changes']
                    summary += f"  📊 资金流向排名变化:\n"

                    if frc.get('ranking_changes'):
                        summary += f"    📈 排名变化:\n"
                        for r in frc['ranking_changes'][:3]:
                            direction = "↗️" if r['rank_change'] > 0 else "↘️"
                            summary += f"      {direction} {r['name']}: 第{r['previous_rank']}位 → 第{r['current_rank']}位 | {r['current_flow_formatted']}\n"

                    if frc.get('new_top10'):
                        summary += f"    ✅ 新进前10:\n"
                        for detail in frc['new_top10']:
                            summary += f"      - {detail['name']}: 第{detail['rank']}位 | {detail['flow_formatted']}\n"

                    if frc.get('lost_top10'):
                        summary += f"    ❌ 跌出前10:\n"
                        for detail in frc['lost_top10']:
                            summary += f"      - {detail['name']}: 原第{detail['previous_rank']}位 | {detail['flow_formatted']}\n"

        # [新增] 添加资金流动态变化对比
        flow_changes = self._generate_flow_changes_summary(time_key, analysis)
        if flow_changes:
            summary += flow_changes

        return summary


    def _update_focus_tracker(self, time_key, analysis):
        """[核心升级] 更新生命周期追踪器"""
        def update_logic(data, tracker_key):
            if not data or not data.get('top_inflow'): return

            items = data['top_inflow']
            if not items: return

            net_col = [k for k in items[0].keys() if '净' in k]
            if not net_col: return
            net_col = net_col[0]

            # 智能识别名称列
            name_col = None
            for col in ['名称', '行业', '板块名称', '概念名称']:
                if col in items[0]:
                    name_col = col
                    break
            if not name_col: return

            for i, item in enumerate(items):
                name = item.get(name_col)
                if not name: continue
                
                history_point = {'time': time_key, 'rank': i + 1, 'flow': float(item.get(net_col, 0))}

                if name not in self.focus_tracker[tracker_key]:
                    self.focus_tracker[tracker_key][name] = {
                        'start_time': time_key, 'end_time': time_key, 'history': [history_point]
                    }
                else:
                    self.focus_tracker[tracker_key][name]['history'].append(history_point)
                    self.focus_tracker[tracker_key][name]['end_time'] = time_key
        
        if 'concept_flow' in analysis['details']: update_logic(analysis['details']['concept_flow'], 'concepts')
        if 'sector_flow' in analysis['details']: update_logic(analysis['details']['sector_flow'], 'sectors')

        # [新增] 更新资金流追踪器
        self._update_flow_tracker(time_key, analysis)

    def _update_flow_tracker(self, time_key, analysis):
        """更新资金流追踪器，记录每个时间点的排名和金额变化"""

        def update_tracker(flow_data, tracker_key, name_keys, net_keys):
            """通用的追踪器更新逻辑"""
            if not flow_data or not flow_data.get('top_inflow'):
                return

            for rank, item in enumerate(flow_data['top_inflow'][:10], 1):  # 追踪前10名
                # 智能识别名称
                name = None
                for name_key in name_keys:
                    if name_key in item:
                        name = item[name_key]
                        break

                if not name:
                    continue

                # 智能识别净流入金额
                net_flow = 0
                for net_key in net_keys:
                    if net_key in item:
                        net_flow = item[net_key]
                        break

                # 转换为万元统一单位
                if isinstance(net_flow, (int, float)):
                    if abs(net_flow) > 10000:  # 大于1万，可能是元，转换为万元
                        net_flow = net_flow / 10000

                # 更新追踪器
                if name not in self.flow_tracker[tracker_key]:
                    self.flow_tracker[tracker_key][name] = []

                self.flow_tracker[tracker_key][name].append({
                    'time': time_key,
                    'rank': rank,
                    'flow': net_flow
                })

        # 更新个股资金流追踪
        if 'fund_flow' in analysis['details']:
            update_tracker(
                analysis['details']['fund_flow'],
                'fund_flow',
                ['名称', 'name', '股票简称'],
                ['main_net_inflow', '今日主力净流入-净额', '净额', '净流入']
            )

        # 更新概念板块资金流追踪
        if 'concept_flow' in analysis['details']:
            update_tracker(
                analysis['details']['concept_flow'],
                'concept_flow',
                ['名称', '概念名称', '板块名称'],
                ['今日主力净流入-净额', '净额', '净流入']
            )

        # 更新行业板块资金流追踪
        if 'sector_flow' in analysis['details']:
            update_tracker(
                analysis['details']['sector_flow'],
                'sector_flow',
                ['名称', '行业', '板块名称'],
                ['今日主力净流入-净额', '净额', '净流入']
            )

    def _generate_flow_changes_summary(self, time_key, analysis):
        """生成资金流变化对比总结"""
        summary = ""

        def analyze_changes(tracker_key, title, emoji):
            """分析单个类型的资金流变化"""
            changes_text = ""
            if tracker_key not in self.flow_tracker:
                return changes_text

            significant_changes = []

            for name, history in self.flow_tracker[tracker_key].items():
                if len(history) < 2:  # 至少需要2个时间点才能对比
                    continue

                current = history[-1]  # 当前时间点
                previous = history[-2]  # 上一个时间点
                first = history[0]     # 第一个时间点（早上开始）

                # 检查是否是当前时间点的数据
                if current['time'] != time_key:
                    continue

                # 排名变化分析
                rank_change = previous['rank'] - current['rank']  # 正数表示排名上升
                flow_change = current['flow'] - previous['flow']  # 资金变化
                flow_change_from_start = current['flow'] - first['flow']  # 从早上开始的变化

                # 判断是否为重要变化
                is_significant = False
                change_desc = []

                # 排名显著变化（上升/下降3名以上）
                if abs(rank_change) >= 3:
                    is_significant = True
                    if rank_change > 0:
                        change_desc.append(f"排名↑{rank_change}位({previous['rank']}→{current['rank']})")
                    else:
                        change_desc.append(f"排名↓{abs(rank_change)}位({previous['rank']}→{current['rank']})")

                # 资金显著变化（变化超过1000万或变化幅度超过50%）
                if abs(flow_change) >= 1000 or (previous['flow'] > 0 and abs(flow_change / previous['flow']) >= 0.5):
                    is_significant = True
                    if flow_change > 0:
                        change_desc.append(f"单期资金流入↑{flow_change:.0f}万({previous['flow']:.0f}→{current['flow']:.0f}万)")
                    else:
                        change_desc.append(f"单期资金流出↓{abs(flow_change):.0f}万({previous['flow']:.0f}→{current['flow']:.0f}万)")

                # 从早上开始的累计变化（变化超过2000万）
                if abs(flow_change_from_start) >= 2000:
                    is_significant = True
                    if flow_change_from_start > 0:
                        change_desc.append(f"全天累计流入↑{flow_change_from_start:.0f}万")
                    else:
                        change_desc.append(f"全天累计流出↓{abs(flow_change_from_start):.0f}万")

                if is_significant:
                    significant_changes.append({
                        'name': name,
                        'changes': change_desc,
                        'current_rank': current['rank'],
                        'current_flow': current['flow'],
                        'rank_change': rank_change,
                        'flow_change': flow_change,
                        'flow_change_from_start': flow_change_from_start
                    })

            # 生成变化报告
            if significant_changes:
                # 按当前排名排序
                significant_changes.sort(key=lambda x: x['current_rank'])
                changes_text += f"\n{emoji} {title}资金流变化:\n"

                for change in significant_changes[:5]:  # 只显示前5个重要变化
                    # 添加当前排名信息
                    rank_info = f"第{change['current_rank']}名"
                    changes_text += f"  - {change['name']}({rank_info}): {' | '.join(change['changes'])}\n"

            # 单独分析排名变化（不依赖于资金变化）
            all_rank_changes = []
            for name, history in self.flow_tracker[tracker_key].items():
                if len(history) < 2:
                    continue

                current = history[-1]
                previous = history[-2]

                if current['time'] != time_key:
                    continue

                rank_change = previous['rank'] - current['rank']  # 正数表示排名上升

                # 只要有排名变化≥3位就记录
                if abs(rank_change) >= 3:
                    all_rank_changes.append({
                        'name': name,
                        'rank_change': rank_change,
                        'current_rank': current['rank'],
                        'current_flow': current['flow'],
                        'previous_rank': previous['rank']
                    })

            # 生成排名变化报告
            if all_rank_changes:
                changes_text += f"\n{emoji} {title}排名变化:\n"
                all_rank_changes.sort(key=lambda x: abs(x['rank_change']), reverse=True)  # 按排名变化幅度排序

                for change in all_rank_changes[:5]:  # 显示前5个排名变化
                    rank_change = change['rank_change']
                    if rank_change > 0:
                        direction = "↗️"
                        change_text = f"上升{rank_change}位({change['previous_rank']}→{change['current_rank']})"
                    else:
                        direction = "↘️"
                        change_text = f"下降{abs(rank_change)}位({change['previous_rank']}→{change['current_rank']})"

                    changes_text += f"  - {direction} {change['name']}: {change_text} | 当前资金{change['current_flow']:.0f}万\n"

            return changes_text

        # 分析各类资金流变化
        summary += analyze_changes('fund_flow', '个股', '💰')
        summary += analyze_changes('concept_flow', '概念板块', '🎯')
        summary += analyze_changes('sector_flow', '行业板块', '🏭')

        return summary

    def _generate_yesterday_comparison_summary(self, current_analysis):
        """[核心升级] 生成开盘与昨日对比的总结战报"""
        if not self.yesterday_snapshot['zt_pool']:
            return ""  # 如果没有昨日数据，则不生成

        summary = f"\n{'=' * 20} 🌞 开盘多空博弈 (对比昨日) {'=' * 20}\n"

        # 获取当前涨停股票名称，优先使用稍后时间点的数据
        current_zt_names = set()

        # 首先尝试从当前分析中获取
        if 'zt_pool' in current_analysis['details'] and current_analysis['details']['zt_pool']['zt_stocks']:
            current_zt_names = {s['名称'] for s in current_analysis['details']['zt_pool']['zt_stocks']}

        # 如果当前时间点没有涨停池数据，尝试查找稍后的涨停池文件
        if not current_zt_names:
            # 直接查找09:31的涨停池文件
            zt_files_0931 = glob.glob(os.path.join(self.date_dir, '09-31_zt_pool.csv'))
            if zt_files_0931:
                zt_analysis = self.analyze_zt_pool('09:31', [os.path.basename(zt_files_0931[0])])
                if zt_analysis['zt_stocks']:
                    current_zt_names = {s['名称'] for s in zt_analysis['zt_stocks']}

            # 如果09:31没有，尝试09:32
            if not current_zt_names:
                zt_files_0932 = glob.glob(os.path.join(self.date_dir, '09-32_zt_pool.csv'))
                if zt_files_0932:
                    zt_analysis = self.analyze_zt_pool('09:32', [os.path.basename(zt_files_0932[0])])
                    if zt_analysis['zt_stocks']:
                        current_zt_names = {s['名称'] for s in zt_analysis['zt_stocks']}

        yesterday_zt_stocks = self.yesterday_snapshot['zt_pool']

        # 1. [核心升级] 龙头试金石模块
        summary += f"  🐉 **龙头试金石**:\n"
        # 筛选出昨日连板数大于等于2的龙头股
        yesterday_dragons = {name: board for name, board in yesterday_zt_stocks.items() if board >= 2}
        if yesterday_dragons:
            # 按连板数降序排序
            sorted_dragons = sorted(yesterday_dragons.items(), key=lambda item: item[1], reverse=True)
            for name, board in sorted_dragons[:5]:  # 最多展示5只龙头
                status = "✅ 晋级" if name in current_zt_names else "❌ 淘汰"
                summary += f"    - {name} (昨日{board}板): {status}\n"
        else:
            summary += "    - 昨日无核心连板龙头。\n"

        # 2. 涨停池整体晋级与淘汰分析
        promoted = set(yesterday_zt_stocks.keys()) & current_zt_names
        eliminated = set(yesterday_zt_stocks.keys()) - current_zt_names

        promotion_rate = (len(promoted) / len(yesterday_zt_stocks)) * 100 if yesterday_zt_stocks else 0

        summary += f"  📈 **昨日涨停梯队**:\n"
        summary += f"    - 总体晋级率: {promotion_rate:.1f}% ({len(promoted)}/{len(yesterday_zt_stocks)})\n"

        # 详细连板进阶分析
        board_progression = {}  # {连板数: [股票名称]}
        for stock_name in promoted:
            if stock_name in yesterday_zt_stocks:
                yesterday_boards = yesterday_zt_stocks[stock_name]
                today_boards = yesterday_boards + 1  # 今日连板数 = 昨日连板数 + 1
                progression_key = f"{yesterday_boards}进{today_boards}"
                if progression_key not in board_progression:
                    board_progression[progression_key] = []
                board_progression[progression_key].append(stock_name)

        # 按连板数排序显示
        if board_progression:
            summary += f"    - 连板进阶详情:\n"
            for progression in sorted(board_progression.keys(), key=lambda x: int(x.split('进')[0])):
                stocks = board_progression[progression]
                summary += f"      * {progression}: {len(stocks)}只 - {', '.join(stocks)}\n"

        if eliminated:
            summary += f"    - 淘汰代表: {', '.join(list(eliminated))}\n"

        # 3. 核心板块强度对比
        if 'concept_flow' in current_analysis['details'] and current_analysis['details']['concept_flow']['top_inflow']:
            top_inflow_items = current_analysis['details']['concept_flow']['top_inflow'][:3]
            current_top3 = set()

            for item in top_inflow_items:
                # 智能识别名称
                name = item.get('名称', item.get('行业', item.get('板块名称', item.get('概念名称', ''))))
                if name:
                    current_top3.add(name)

            continued_strength = self.yesterday_snapshot['concept_top3'] & current_top3
            faded_strength = self.yesterday_snapshot['concept_top3'] - current_top3

            summary += f"  🎯 **昨日核心板块**:\n"
            if continued_strength:
                summary += f"    - 强度延续: {', '.join(continued_strength)}\n"
            if faded_strength:
                summary += f"    - 强度退潮: {', '.join(faded_strength)}\n"

        summary += "=" * 60 + "\n"
        return summary

    def generate_daily_report(self):
            """生成完整的日报告"""
            # 首先清理非交易时间的文件
            deleted_count = self.clean_non_trading_files()
            if deleted_count > 0:
                print(f"已清理 {deleted_count} 个非交易时间文件")

            if not self.scan_files(): return None

            # [核心升级] 在分析开始前加载昨日快照
            self._load_yesterday_snapshot()

            sorted_times = sorted(self.time_data.keys())
            time_analyses = {t: self.analyze_time_period(t, self.time_data[t]) for t in sorted_times}

            # 过滤出真正的时间点（排除"全天数据"）
            real_times = [t for t in sorted_times if t != "全天数据"]
            time_range = f"{real_times[0]} - {real_times[-1]}" if real_times else "无时间数据"

            report = f"""
    {'=' * 80}
                        {self.analysis_date} 股市数据分析报告
    {'=' * 80}

    📊 数据概览:
    - 分析日期: {self.analysis_date}
    - 数据时间段: {time_range}
    - 总时间点: {len(real_times)}个
    - 数据文件总数: {sum(len(v) for d in self.time_data.values() for v in d.values())}个
    """
            previous_analysis = None
            for i, time_key in enumerate(sorted_times):
                analysis = time_analyses[time_key]

                # [核心升级] 在开盘特定时间点插入昨日对比战报
                if i > 0 and i < 6:  # 在第二个到第六个时间点插入，通常覆盖09:35-09:45
                    report += self._generate_yesterday_comparison_summary(analysis)

                # [核心升级] 更新追踪器
                self._update_focus_tracker(time_key, analysis)

                report += self.generate_time_summary(time_key, analysis, previous_analysis)
                previous_analysis = analysis

            report += self.generate_daily_summary(time_analyses, sorted_times)
            return report

    def _load_yesterday_snapshot(self):
        """[核心升级] 加载前一日的收盘核心数据作为快照"""
        try:
            today = datetime.strptime(self.analysis_date, '%Y-%m-%d')
            # 简单处理，直接减一天，实际应用中需考虑节假日
            yesterday = today - timedelta(days=1)
            yesterday_str = yesterday.strftime('%Y-%m-%d')
            yesterday_dir = os.path.join(self.data_dir, yesterday_str)

            if not os.path.exists(yesterday_dir):
                print(f"提示: 未找到昨日({yesterday_str})数据，无法进行开盘对比。")
                return

            # 加载昨日收盘涨停池，并记录名称和连板数
            zt_files = glob.glob(os.path.join(yesterday_dir, '*15-0*zt_pool.csv'))
            if not zt_files:
                # 如果没有找到15-0*格式，尝试15-00格式
                zt_files = glob.glob(os.path.join(yesterday_dir, '15-00_zt_pool.csv'))
            if zt_files:
                df_zt = self.load_csv_safe(zt_files[0])
                if df_zt is not None and '名称' in df_zt.columns and '连板数' in df_zt.columns:
                    # 使用Series和to_dict快速创建 名称->连板数 的映射
                    self.yesterday_snapshot['zt_pool'] = pd.Series(df_zt['连板数'].values, index=df_zt['名称']).to_dict()

            # 加载昨日收盘概念板块资金流前三
            concept_files = glob.glob(os.path.join(yesterday_dir, '*15-0*concept_fund_flow_*.csv'))
            if not concept_files:
                # 如果没有找到15-0*格式，尝试其他收盘时间格式
                concept_files = glob.glob(os.path.join(yesterday_dir, '14-59_concept_fund_flow_*.csv'))
                if not concept_files:
                    concept_files = glob.glob(os.path.join(yesterday_dir, '15-00_concept_fund_flow_*.csv'))
            if concept_files:
                df_concept = self.load_csv_safe(concept_files[0])
                net_cols = [c for c in df_concept.columns if '净' in c]
                if df_concept is not None and net_cols:
                    # 智能识别名称列
                    name_col = None
                    for col in ['名称', '行业', '板块名称', '概念名称']:
                        if col in df_concept.columns:
                            name_col = col
                            break

                    if name_col:
                        df_concept[net_cols[0]] = pd.to_numeric(df_concept[net_cols[0]], errors='coerce')
                        self.yesterday_snapshot['concept_top3'] = set(df_concept.nlargest(3, net_cols[0])[name_col])

            print("✅ 昨日核心数据快照加载成功。")

        except Exception as e:
            print(f"❌ 加载昨日快照失败: {e}")



    def _generate_focus_summary(self):
        """[核心升级] 生成焦点板块的生命周期复盘总结"""
        focus_summary = f"\n\n🔥 焦点概念全天复盘:\n"
        
        scored_items = []
        # 以概念板块为例进行评分和筛选
        for name, data in self.focus_tracker['concepts'].items():
            history = data['history']
            if not history or len(history) < 3: continue
            
            peak_flow = max(h['flow'] for h in history)
            min_rank = min(h['rank'] for h in history)
            duration = len(history)
            
            score = (peak_flow / 1e8) + (duration * 2) + ((1 / min_rank) * 20)
            if min_rank <= 5 or duration > 10:
                scored_items.append({'name': name, 'score': score, 'data': data})
                
        top_items = sorted(scored_items, key=lambda x: x['score'], reverse=True)[:3]
        if not top_items: return ""

        for item in top_items:
            name, data = item['name'], item['data']
            history = data['history']
            
            active_period = f"{data['start_time']} - {data['end_time']}"
            peak = max(history, key=lambda x: x['flow'])
            peak_flow_str = f"{peak['flow'] / 1e8:.2f}亿" if abs(peak['flow']) > 1e8 else f"{peak['flow'] / 1e4:.0f}万"
            
            surpassed_info = ""
            if peak['rank'] == 1:
                for h in history:
                    if h['time'] > peak['time'] and h['rank'] > 1:
                        surpassed_info = f"，于{h['time']}被反超"
                        break
            
            fund_path = f"早盘强势流入，于{peak['time']}达到峰值({peak_flow_str}){surpassed_info}，随后资金动能衰减。"
            
            interpretation = "盘中热点，持续性有待观察。"
            if peak['rank'] == 1 and history[-1]['flow'] < peak['flow'] * 0.5 and history[-1]['rank'] > 3:
                interpretation = "旧周期核心，高位分歧，资金获利了结，注意补跌风险。"
            elif history[0]['flow'] < history[-1]['flow'] and history[-1]['rank'] <= 3:
                interpretation = "新周期主线，市场合力方向，关注次日延续性。"

            focus_summary += f"  - {name}:\n"
            focus_summary += f"    - 活跃时段: {active_period}\n"
            focus_summary += f"    - 资金路径: {fund_path}\n"
            focus_summary += f"    - 盘面解读: {interpretation}\n"
            
        return focus_summary

    def generate_daily_summary(self, time_analyses, sorted_times):
        """生成全天总结"""
        summary = f"\n\n{'=' * 80}\n"
        summary += f"                           全天总结\n"
        summary += f"{'=' * 80}\n"

        all_zt_stocks = set()
        max_zt_count, max_zt_time = 0, ""
        for time_key, analysis in time_analyses.items():
            if 'zt_pool' in analysis['details'] and analysis['details']['zt_pool']['stats']:
                zt_data = analysis['details']['zt_pool']
                # 确保'代码'列存在
                if zt_data['zt_stocks'] and '代码' in zt_data['zt_stocks'][0]:
                    for stock in zt_data['zt_stocks']:
                        all_zt_stocks.add(stock['代码'])
                if zt_data['stats']['total_zt'] > max_zt_count:
                    max_zt_count, max_zt_time = zt_data['stats']['total_zt'], time_key

        # [新增] 昨日涨停股今日表现分析
        yesterday_performance_summary = self._analyze_yesterday_zt_performance(time_analyses, sorted_times)

        summary += f"\n📈 涨停池统计:\n"
        summary += f"  - 全天涨停股票总数: {len(all_zt_stocks)}只\n"
        summary += f"  - 最高涨停数量: {max_zt_count}只 (时间: {max_zt_time})\n"

        # [新增] 炸板股池统计 - 使用15点后的收盘数据
        eod_zb_summary = self._get_eod_zb_pool_summary()
        if eod_zb_summary:
            summary += eod_zb_summary

        # [新增] 添加昨日涨停股今日表现
        summary += yesterday_performance_summary

        # [新增] 涨停股池概念/行业分析
        zt_analysis_summary = self._analyze_zt_pool_distribution(time_analyses, sorted_times)
        summary += zt_analysis_summary

        active_periods = []
        for time_key, analysis in time_analyses.items():
            score = 0
            if 'zt_pool' in analysis['details'] and analysis['details']['zt_pool']['stats']: score += \
            analysis['details']['zt_pool']['stats']['total_zt']
            if 'news' in analysis['details']: score += len(analysis['details']['news']['news_list'])
            if 'movers' in analysis['details']: score += len(analysis['details']['movers']['movers'])
            active_periods.append((time_key, score))
        active_periods.sort(key=lambda x: x[1], reverse=True)

        summary += f"\n🔥 最活跃时间段:\n"
        for i, (time_key, score) in enumerate(active_periods[:5]):
            summary += f"  {i + 1}. {time_key} (活跃度: {score})\n"

        # [核心升级] 新增全天资金战果总览模块
        summary += f"\n💰 全天资金战果总览 (以收盘数据为准):\n"

        # 寻找最后一个有板块资金流数据的时间点
        last_flow_time = None
        for t in reversed(sorted_times):
            if time_analyses.get(t, {}).get('details', {}).get('concept_flow'):
                last_flow_time = t
                break

        if last_flow_time:
            last_analysis = time_analyses[last_flow_time]

            def format_eod_flow(board_data, board_type_name):
                res = ""
                # 确保 board_data 是字典且包含 full_data
                if isinstance(board_data, dict) and 'full_data' in board_data and not board_data['full_data'].empty:
                    res += f"  - **{board_type_name}**:\n"
                    df = board_data['full_data']

                    # 动态寻找净流入列
                    net_cols = [c for c in df.columns if '净' in c]
                    if not net_cols: return ""
                    net_col = net_cols[0]

                    # 智能识别名称列
                    name_col = None
                    for col in ['名称', '行业', '板块名称', '概念名称']:
                        if col in df.columns:
                            name_col = col
                            break
                    if not name_col: return ""

                    top_inflow = df.nlargest(3, net_col)
                    top_outflow = df.nsmallest(3, net_col)

                    inflow_texts = []
                    for _, row in top_inflow.iterrows():
                        flow_val = row[net_col]
                        flow_amount = f"({flow_val / 1e8:.2f}亿)" if abs(
                            flow_val) >= 1e8 else f"({flow_val / 1e4:.0f}万)"
                        inflow_texts.append(f"{row[name_col]} {flow_amount}")
                    res += f"    - 流入主力: {' | '.join(inflow_texts)}\n"

                    outflow_texts = []
                    for _, row in top_outflow.iterrows():
                        flow_val = row[net_col]
                        flow_amount = f"({flow_val / 1e8:.2f}亿)" if abs(
                            flow_val) >= 1e8 else f"({flow_val / 1e4:.0f}万)"
                        outflow_texts.append(f"{row[name_col]} {flow_amount}")
                    res += f"    - 流出主力: {' | '.join(outflow_texts)}\n"
                return res

            if 'details' in last_analysis:
                if 'concept_flow' in last_analysis['details']:
                    summary += format_eod_flow(last_analysis['details']['concept_flow'], "概念板块")
                if 'sector_flow' in last_analysis['details']:
                    summary += format_eod_flow(last_analysis['details']['sector_flow'], "行业板块")
        else:
            summary += "  - 未能获取到收盘时的板块资金流数据。\n"

        summary += f"\n📝 分析建议:\n"
        summary += f"  - 关注{max_zt_time}时段的市场动向，涨停股票最多\n"
        summary += f"  - 重点关注活跃时间段的资金流向变化\n"
        summary += f"  - 建议复盘全天{len(all_zt_stocks)}只涨停股票的表现\n"

        # [核心升级] 添加焦点复盘总结
        summary += self._generate_focus_summary()

        return summary

    def _get_eod_zb_pool_summary(self, include_list=False):
        """获取15点后的炸板股池收盘数据统计"""
        import glob

        # 查找15点后的zb_pool文件
        eod_zb_files = []

        # 格式1: zb_pool_YYYYMMDD_HHMMSS.csv (15点后)
        pattern1 = os.path.join(self.date_dir, 'zb_pool_*_15*.csv')
        eod_zb_files.extend(glob.glob(pattern1))

        # 格式2: 15-XX_zb_pool.csv
        pattern2 = os.path.join(self.date_dir, '15-*_zb_pool.csv')
        eod_zb_files.extend(glob.glob(pattern2))

        # 格式3: 炸板股池_*_15*.csv
        pattern3 = os.path.join(self.date_dir, '炸板股池_*_15*.csv')
        eod_zb_files.extend(glob.glob(pattern3))

        if not eod_zb_files:
            return ""

        # 选择最新的文件
        eod_zb_files.sort()
        latest_zb_file = eod_zb_files[-1]

        # 分析炸板股池数据
        zb_analysis = self.analyze_zb_pool("15:00+", [os.path.basename(latest_zb_file)])

        if not zb_analysis['stats']:
            return ""

        summary = f"\n💥 炸板股池情况:\n"
        summary += f"  - 炸板股票数量: {zb_analysis['stats']['total_zb']}只\n"

        if zb_analysis['stats']['industries']:
            top_ind = max(zb_analysis['stats']['industries'].items(), key=lambda x: x[1])
            summary += f"  - 热门行业: {top_ind[0]} ({top_ind[1]}只)\n"

        if zb_analysis['stats'].get('avg_amplitude', 0) > 0:
            summary += f"  - 平均振幅: {zb_analysis['stats']['avg_amplitude']:.1f}%\n"

        # 如果需要包含股票列表
        if include_list and zb_analysis['zb_stocks'] and '代码' in zb_analysis['zb_stocks'][0]:
            summary += f"  - 炸板股票列表:\n"
            for i, stock in enumerate(zb_analysis['zb_stocks'], 1):
                # 获取振幅信息
                amplitude_info = ""
                if stock.get('振幅', 0) > 0:
                    amplitude_info = f", 振幅{stock['振幅']:.1f}%"

                # 获取炸板次数信息
                zb_info = ""
                if stock.get('炸板次数', 0) > 0:
                    zb_info = f", {stock['炸板次数']}次炸板"

                # 获取行业信息
                industry_info = ""
                if stock.get('所属行业'):
                    industry_info = f", {stock['所属行业']}"

                summary += f"    {i:2d}. {stock['名称']}({stock['代码']}{industry_info}{amplitude_info}{zb_info})\n"

                # 限制显示数量
                if i >= 10:
                    break

        return summary

    def _analyze_yesterday_zt_performance(self, time_analyses, sorted_times):
        """分析昨日涨停股今日表现"""
        if not self.yesterday_snapshot or 'zt_pool' not in self.yesterday_snapshot:
            return ""

        yesterday_zt_stocks = self.yesterday_snapshot['zt_pool']
        yesterday_total = len(yesterday_zt_stocks)

        if yesterday_total == 0:
            return ""

        # 获取昨日涨停股今日表现数据（使用previous_zt_pool文件最准确）
        today_zt_names = set()
        yesterday_performance_data = None

        # 优先查找previous_zt_pool文件，这个文件包含昨日涨停股今日的表现
        import glob
        previous_zt_files = glob.glob(os.path.join(self.date_dir, '*previous_zt_pool.csv'))
        # 优先使用15:00附近的文件，因为收盘后数据通常在15:00左右固定
        if previous_zt_files:
            # 按时间排序，优先选择15:00附近的文件
            eod_files = [f for f in previous_zt_files if any(time in f for time in ['15-0', '16-0', '14-5'])]
            if eod_files:
                eod_files.sort()
                previous_zt_file = eod_files[-1]  # 使用最新的收盘文件
            else:
                previous_zt_files.sort()
                previous_zt_file = previous_zt_files[-1]  # 使用最新的文件
            df_previous = self.load_csv_safe(previous_zt_file)
            if df_previous is not None and '名称' in df_previous.columns and '涨跌幅' in df_previous.columns:
                yesterday_performance_data = df_previous
                # 统计今日继续涨停的股票（涨跌幅>=9.9%）
                continued_zt_mask = df_previous['涨跌幅'] >= 9.9
                today_zt_names = set(df_previous[continued_zt_mask]['名称'].tolist())

        # 如果没有previous_zt_pool文件，使用收盘涨停池数据
        if not today_zt_names:
            # 优先使用收盘时间的涨停池数据
            for time_key in reversed(sorted_times):
                if time_key in time_analyses:
                    analysis = time_analyses[time_key]
                    if 'zt_pool' in analysis['details'] and analysis['details']['zt_pool']['zt_stocks']:
                        today_zt_names = {s['名称'] for s in analysis['details']['zt_pool']['zt_stocks']}
                        break

            # 如果还没有找到，尝试查找收盘涨停池文件
            if not today_zt_names:
                zt_files_eod = glob.glob(os.path.join(self.date_dir, '*16-0*zt_pool.csv'))
                if not zt_files_eod:
                    zt_files_eod = glob.glob(os.path.join(self.date_dir, '*15-0*zt_pool.csv'))
                if zt_files_eod:
                    zt_analysis = self.analyze_zt_pool('收盘', [os.path.basename(zt_files_eod[0])])
                    if zt_analysis['zt_stocks']:
                        today_zt_names = {s['名称'] for s in zt_analysis['zt_stocks']}

        # 分析昨日涨停股今日表现
        if yesterday_performance_data is not None:
            # 使用previous_zt_pool数据进行精确分析
            total_yesterday = len(yesterday_performance_data)
            continued_zt_mask = yesterday_performance_data['涨跌幅'] >= 9.9
            continued_zt_count = continued_zt_mask.sum()
            not_zt_count = total_yesterday - continued_zt_count

            continued_zt_names = yesterday_performance_data[continued_zt_mask]['名称'].tolist()
            not_zt_names = yesterday_performance_data[~continued_zt_mask]['名称'].tolist()

            summary = f"\n🎯 昨日涨停股今日表现:\n"
            summary += f"  - 昨日涨停总数: {total_yesterday}只\n"
            summary += f"  - 今日继续涨停: {continued_zt_count}只 ({continued_zt_count/total_yesterday*100:.1f}%)\n"
            summary += f"  - 今日未涨停: {not_zt_count}只 ({not_zt_count/total_yesterday*100:.1f}%)\n"

            if continued_zt_names:
                summary += f"  - 继续涨停股票: {', '.join(continued_zt_names)}\n"

            if not_zt_names:
                summary += f"  - 未涨停股票: {', '.join(not_zt_names)}\n"
        else:
            # 回退到原有逻辑
            continued_zt = set(yesterday_zt_stocks.keys()) & today_zt_names  # 继续涨停
            not_zt_today = set(yesterday_zt_stocks.keys()) - today_zt_names  # 今日未涨停

            summary = f"\n🎯 昨日涨停股今日表现:\n"
            summary += f"  - 昨日涨停总数: {yesterday_total}只\n"
            summary += f"  - 今日继续涨停: {len(continued_zt)}只 ({len(continued_zt)/yesterday_total*100:.1f}%)\n"
            summary += f"  - 今日未涨停: {len(not_zt_today)}只 ({len(not_zt_today)/yesterday_total*100:.1f}%)\n"

            if continued_zt:
                summary += f"  - 继续涨停股票: {', '.join(list(continued_zt))}\n"

            if not_zt_today:
                summary += f"  - 未涨停股票: {', '.join(list(not_zt_today))}\n"

        return summary

    def _analyze_zt_pool_distribution(self, time_analyses, sorted_times):
        """分析涨停股池中的概念/行业分布和炸板情况"""
        # 优先获取收盘时间（15:00附近）的涨停池数据
        last_zt_data = None

        # 首先尝试收盘时间的数据
        eod_times = [t for t in sorted_times if any(prefix in t for prefix in ['15:', '16:', '14:5'])]
        for time_key in reversed(eod_times):
            if time_key in time_analyses:
                analysis = time_analyses[time_key]
                if 'zt_pool' in analysis['details'] and analysis['details']['zt_pool']['zt_stocks']:
                    last_zt_data = analysis['details']['zt_pool']['zt_stocks']
                    break

        # 如果收盘时间没有数据，使用最后一个有数据的时间点
        if not last_zt_data:
            for time_key in reversed(sorted_times):
                if time_key in time_analyses:
                    analysis = time_analyses[time_key]
                    if 'zt_pool' in analysis['details'] and analysis['details']['zt_pool']['zt_stocks']:
                        last_zt_data = analysis['details']['zt_pool']['zt_stocks']
                        break

        if not last_zt_data:
            # 如果还没有数据，尝试直接读取收盘涨停池文件
            import glob
            zt_files_eod = glob.glob(os.path.join(self.date_dir, '*15-0*zt_pool.csv'))
            if not zt_files_eod:
                zt_files_eod = glob.glob(os.path.join(self.date_dir, '*16-0*zt_pool.csv'))
            if zt_files_eod:
                zt_files_eod.sort()
                zt_analysis = self.analyze_zt_pool('收盘', [os.path.basename(zt_files_eod[-1])])
                if zt_analysis['zt_stocks']:
                    last_zt_data = zt_analysis['zt_stocks']

        if not last_zt_data:
            return ""

        # 统计概念/行业分布
        industry_count = {}
        concept_count = {}
        zhaban_by_industry = {}
        zhaban_by_concept = {}

        for stock in last_zt_data:
            # 行业统计
            if '所属行业' in stock and stock['所属行业']:
                industry = stock['所属行业']
                industry_count[industry] = industry_count.get(industry, 0) + 1

                # 炸板次数统计
                zhaban_times = stock.get('炸板次数', 0)
                if isinstance(zhaban_times, (int, float)) and zhaban_times > 0:
                    if industry not in zhaban_by_industry:
                        zhaban_by_industry[industry] = []
                    zhaban_by_industry[industry].append(zhaban_times)

            # 概念统计（如果有概念字段的话）
            if '概念' in stock and stock['概念']:
                concepts = stock['概念'].split(',') if isinstance(stock['概念'], str) else [stock['概念']]
                for concept in concepts:
                    concept = concept.strip()
                    if concept:
                        concept_count[concept] = concept_count.get(concept, 0) + 1

                        # 炸板次数统计
                        zhaban_times = stock.get('炸板次数', 0)
                        if isinstance(zhaban_times, (int, float)) and zhaban_times > 0:
                            if concept not in zhaban_by_concept:
                                zhaban_by_concept[concept] = []
                            zhaban_by_concept[concept].append(zhaban_times)

        summary = f"\n📊 涨停股池分布分析:\n"

        # 行业分布
        if industry_count:
            sorted_industries = sorted(industry_count.items(), key=lambda x: x[1], reverse=True)
            summary += f"  - **行业分布** (前5名):\n"
            for i, (industry, count) in enumerate(sorted_industries[:5]):
                summary += f"    {i+1}. {industry}: {count}只\n"

        # 概念分布（如果有的话）
        if concept_count:
            sorted_concepts = sorted(concept_count.items(), key=lambda x: x[1], reverse=True)
            summary += f"  - **概念分布** (前5名):\n"
            for i, (concept, count) in enumerate(sorted_concepts[:5]):
                summary += f"    {i+1}. {concept}: {count}只\n"

        # 炸板分析
        summary += f"\n💥 炸板情况分析:\n"

        # 按行业统计炸板
        if zhaban_by_industry:
            industry_zhaban_stats = {}
            for industry, zhaban_list in zhaban_by_industry.items():
                total_zhaban = sum(zhaban_list)
                avg_zhaban = total_zhaban / len(zhaban_list)
                industry_zhaban_stats[industry] = {
                    'total': total_zhaban,
                    'avg': avg_zhaban,
                    'count': len(zhaban_list)
                }

            # 按总炸板次数排序
            sorted_zhaban_industries = sorted(industry_zhaban_stats.items(),
                                            key=lambda x: x[1]['total'], reverse=True)

            summary += f"  - **炸板最多的行业** (前3名):\n"
            for i, (industry, stats) in enumerate(sorted_zhaban_industries[:3]):
                summary += f"    {i+1}. {industry}: 总计{stats['total']}次 (平均{stats['avg']:.1f}次/股, {stats['count']}只股票)\n"

        # 按概念统计炸板（如果有的话）
        if zhaban_by_concept:
            concept_zhaban_stats = {}
            for concept, zhaban_list in zhaban_by_concept.items():
                total_zhaban = sum(zhaban_list)
                avg_zhaban = total_zhaban / len(zhaban_list)
                concept_zhaban_stats[concept] = {
                    'total': total_zhaban,
                    'avg': avg_zhaban,
                    'count': len(zhaban_list)
                }

            # 按总炸板次数排序
            sorted_zhaban_concepts = sorted(concept_zhaban_stats.items(),
                                          key=lambda x: x[1]['total'], reverse=True)

            summary += f"  - **炸板最多的概念** (前3名):\n"
            for i, (concept, stats) in enumerate(sorted_zhaban_concepts[:3]):
                summary += f"    {i+1}. {concept}: 总计{stats['total']}次 (平均{stats['avg']:.1f}次/股, {stats['count']}只股票)\n"

        if not zhaban_by_industry and not zhaban_by_concept:
            summary += f"  - 今日涨停股票炸板情况较少，市场情绪相对稳定\n"

        return summary

    def save_report(self, report):
        """保存报告到文件"""
        if not os.path.exists(OUTPUT_DIR): os.makedirs(OUTPUT_DIR)
        filename = f"{OUTPUT_DIR}/fund_analysis_report_{self.analysis_date}.txt"
        with open(filename, 'w', encoding='utf-8') as f: f.write(report)
        print(f"报告已保存到: {filename}")
        return filename

def main():
    """主函数"""
    print(f"开始分析 {ANALYSIS_DATE} 的基金数据...")
    analyzer = FundDataAnalyzer(ANALYSIS_DATE, FUND_DATA_DIR)
    report = analyzer.generate_daily_report()
    if report:
        # print(report) # 报告较长，默认不打印到控制台
        analyzer.save_report(report)
        print(f"\n✅ 分析完成！")
    else:
        print("❌ 分析失败，请检查数据文件夹是否存在")

if __name__ == "__main__":
    main()