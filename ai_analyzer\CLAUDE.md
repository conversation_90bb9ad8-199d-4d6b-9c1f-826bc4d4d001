# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## System Architecture

This is an AI-powered Chinese stock market analysis system specializing in limit-up stock (涨停股) detection and analysis. The system processes intraday trading data, post-market data, and uses multiple AI models to generate comprehensive analysis reports.

### Core Components

**Data Processing Pipeline:**
- `zt_detective.py`: Base rule-based analysis engine
- `langchain_zt_detective_Gemini.py`: LangChain integration with Google's Gemini AI
- `langchain_zt_detective_siliconflow.py`: LangChain with SiliconFlow/DeepSeek AI models
- `enhanced_langchain_zt_detective.py`: Advanced AI-powered analysis with 6 specialized tools

**Data Sources Architecture:**
- **Intraday Data**: Time-series fund flow, sector rankings, individual stock rankings
- **Post-Market Data**: Dragon-Tiger List (龙虎榜), technical indicators, bulk trades
- **Historical Data**: Previous trading day data for trend analysis
- **Multi-Provider Support**: TPDog, AkShare, THS, EFinance, AData, Sina

### AI Integration Framework

**Multi-Provider Support:**
- **Gemini API**: For structured analysis using Google's models
- **SiliconFlow**: DeepSeek models with robust API key management
- **Enhanced Version**: 6 specialized analysis tools with exploratory AI capabilities

**API Key Management:**
- Automatic key rotation with invalid key tracking
- Balance checking and validation
- Persistent state management across sessions

## Key Development Commands

### Running Analysis Systems

```bash
# Base analysis system (rule-based)
python zt_detective.py

# Gemini-powered AI analysis
python langchain_zt_detective_Gemini.py

# SiliconFlow/DeepSeek AI analysis
python langchain_zt_detective_siliconflow.py

# Enhanced AI analysis with 6 specialized tools
python enhanced_langchain_zt_detective.py
```

### Environment Setup

```bash
# Install required dependencies
pip install pandas langchain langchain-google-genai langchain-openai python-dotenv requests

# Environment configuration
# Copy .env.example to .env and configure:
# - GOOGLE_API_KEY for Gemini models
# - SILICONFLOW_API_KEYS (comma-separated) for DeepSeek models
# - Optional proxy settings
```

## Configuration Management

### Environment Variables
- `GOOGLE_API_KEY`: Google Gemini API key
- `SILICONFLOW_API_KEYS`: Comma-separated SiliconFlow API keys
- `USE_PROXY`: Enable proxy support (true/false)
- `PROXY_URL`: Proxy server URL

### Data Paths Configuration
- `ANALYSIS_DATE`: Target analysis date (default: current date)
- `FUND_DATA_DIR`: Path to intraday data directory
- `POST_MARKET_DATA_DIR`: Path to post-market static data
- `OUTPUT_DIR`: Directory for generated reports

## Data Flow Architecture

1. **Data Collection**: Multi-source data loading with fallback mechanisms
2. **Time-Series Processing**: Chronological analysis of intraday events
3. **AI Analysis**: LangChain agents with specialized analysis tools
4. **Pattern Recognition**: Signal quantification and pattern classification
5. **Report Generation**: Structured Markdown reports with insights

## Analysis Framework

### Six-Layer Analysis (Enhanced Version)
1. **Surface Level**: Basic stock information collection
2. **Connection Level**: Hidden relationship discovery between stocks
3. **Momentum Level**: Sector momentum and fund flow analysis
4. **Anomaly Level**: Signal pattern detection and anomaly identification
5. **Leadership Level**: Lead-lag relationship analysis
6. **Insight Synthesis**: Comprehensive investment insights

### Specialized AI Tools
- `analyze_sector_momentum`: Sector fund flow trend analysis
- `discover_hidden_connections`: Stock relationship network mapping
- `trace_fund_flow_patterns`: Capital rotation pattern tracking
- `identify_anomaly_signals`: Unusual signal combination detection
- `detect_lead_lag_relationships`: Market leadership analysis
- `generate_insight_driven_report_section`: Dynamic report generation

## File Classification System

The system uses a sophisticated file classification mechanism:
- **Fund Flow Data**: Individual and sector fund flow rankings
- **Limit-up Pools**: Real-time and end-of-day limit-up stock lists
- **Technical Signals**: Price/volume patterns and indicator breakouts
- **Market Data**: Index performance and market-wide indicators
- **News & Events**: Catalyst identification and sentiment analysis

## Output and Reporting

### Report Types Generated
- Individual stock analysis reports
- Comprehensive market overview reports
- Sector rotation analysis reports
- Signal effectiveness statistics
- Risk assessment reports

### Report Structure
- Market overview and statistics
- Sector analysis and rotation patterns
- Individual stock deep dives
- Signal effectiveness metrics
- Risk assessment and recommendations

## Important Notes

- The system requires Chinese stock market data in specific CSV formats
- AI analysis requires valid API keys for respective services
- Data directories must follow the expected structure with date-based organization
- The system is designed for Chinese A-share market analysis specifically
- All timestamps and trading hours follow Chinese market conventions (09:30-15:00)

## Error Handling and Resilience

- Automatic API key rotation and invalid key tracking
- Graceful degradation when data sources are unavailable
- Robust CSV parsing with multiple encoding support
- Comprehensive error logging and status reporting