#!/usr/bin/.env python3
# -*- coding: utf-8 -*-
"""
增强版动态分析器
生成类似zt_pool_analyzer的全面动态变化报告
解决现有报告中缺少动态变化分析的问题
"""

import os
import pandas as pd
import re
from collections import defaultdict
from fund_data_analyzer import FundDataAnalyzer

class EnhancedDynamicAnalyzer:
    def __init__(self, analysis_date, data_dir):
        self.analysis_date = analysis_date
        self.data_dir = data_dir
        self.date_dir = os.path.join(data_dir, analysis_date)
        self.analyzer = FundDataAnalyzer(analysis_date, data_dir)
        
        # 存储所有时间段的数据
        self.time_series_data = {}
        
    def analyze_all_dynamic_changes(self):
        """分析所有数据类型的动态变化"""
        print("🔍 开始全面动态变化分析...")
        print("=" * 80)
        
        # 1. 扫描并加载所有时间段数据
        self.load_time_series_data()
        
        # 2. 分析各类数据的动态变化
        results = {
            'zt_pool_changes': self.analyze_zt_pool_dynamics(),
            'fund_flow_changes': self.analyze_fund_flow_dynamics(),
            'concept_flow_changes': self.analyze_concept_flow_dynamics(),
            'sector_flow_changes': self.analyze_sector_flow_dynamics(),
            'movers_changes': self.analyze_movers_dynamics(),
            'news_changes': self.analyze_news_dynamics(),
            'index_changes': self.analyze_index_dynamics(),
            'big_deal_changes': self.analyze_big_deal_dynamics()
        }
        
        # 3. 生成综合报告
        report = self.generate_comprehensive_dynamic_report(results)
        
        # 4. 保存报告
        self.save_dynamic_report(report)
        
        return results
    
    def load_time_series_data(self):
        """加载所有时间段的数据"""
        if not os.path.exists(self.date_dir):
            print(f"错误：日期文件夹 {self.date_dir} 不存在")
            return
        
        # 扫描文件
        self.analyzer.scan_files()
        self.time_series_data = self.analyzer.time_data
        
        print(f"✅ 已加载 {len(self.time_series_data)} 个时间段的数据")
    
    def analyze_zt_pool_dynamics(self):
        """分析涨停池动态变化（详细版）"""
        print("\n🎯 分析涨停池动态变化...")
        
        zt_changes = []
        previous_stocks = set()
        previous_data = {}
        
        # 获取所有涨停池时间点
        zt_times = []
        for time_key, files_dict in self.time_series_data.items():
            if 'zt_pool' in files_dict and time_key != '全天数据':
                zt_times.append(time_key)
        
        zt_times.sort()
        
        for i, time_key in enumerate(zt_times):
            files_dict = self.time_series_data[time_key]
            current_stocks = set()
            current_data = {}
            
            # 加载当前时间段的涨停数据
            for file in files_dict['zt_pool']:
                filepath = os.path.join(self.date_dir, file)
                try:
                    df = pd.read_csv(filepath, encoding='utf-8')
                    if not df.empty:
                        for _, row in df.iterrows():
                            code = str(row.get('代码', ''))
                            name = str(row.get('名称', ''))
                            industry = str(row.get('所属行业', '未知'))
                            board_count = row.get('连板数', 1)
                            
                            if code and name:
                                stock_key = f"{name}({code})"
                                current_stocks.add(stock_key)
                                current_data[stock_key] = {
                                    'name': name,
                                    'code': code,
                                    'industry': industry,
                                    'board_count': board_count
                                }
                except Exception as e:
                    print(f"读取涨停文件失败 {file}: {e}")
            
            # 计算变化
            if i > 0:  # 跳过第一个时间点
                new_stocks = current_stocks - previous_stocks
                lost_stocks = previous_stocks - current_stocks
                
                # 行业变化统计
                current_industries = defaultdict(int)
                previous_industries = defaultdict(int)
                
                for stock in current_stocks:
                    if stock in current_data:
                        current_industries[current_data[stock]['industry']] += 1
                
                for stock in previous_stocks:
                    if stock in previous_data:
                        previous_industries[previous_data[stock]['industry']] += 1
                
                industry_changes = {}
                all_industries = set(current_industries.keys()) | set(previous_industries.keys())
                for industry in all_industries:
                    curr_count = current_industries[industry]
                    prev_count = previous_industries[industry]
                    if curr_count != prev_count:
                        industry_changes[industry] = {
                            'previous': prev_count,
                            'current': curr_count,
                            'change': curr_count - prev_count
                        }
                
                change_record = {
                    'time': time_key,
                    'total_stocks': len(current_stocks),
                    'total_change': len(current_stocks) - len(previous_stocks),
                    'new_stocks': list(new_stocks),
                    'lost_stocks': list(lost_stocks),
                    'new_count': len(new_stocks),
                    'lost_count': len(lost_stocks),
                    'industry_changes': industry_changes,
                    'current_data': current_data.copy(),
                    'previous_data': previous_data.copy()
                }
                zt_changes.append(change_record)
            
            previous_stocks = current_stocks.copy()
            previous_data = current_data.copy()
        
        return zt_changes
    
    def analyze_fund_flow_dynamics(self):
        """分析资金流向动态变化"""
        print("💰 分析资金流向动态变化...")
        
        flow_changes = []
        previous_top10 = {}
        
        # 获取所有资金流时间点
        flow_times = []
        for time_key, files_dict in self.time_series_data.items():
            if any(key in files_dict for key in ['fund_flow', 'fund_flow_rank', 'main_fund_flow']) and time_key != '全天数据':
                flow_times.append(time_key)
        
        flow_times.sort()
        
        for i, time_key in enumerate(flow_times):
            files_dict = self.time_series_data[time_key]
            current_top10 = {}
            
            # 分析资金流排名数据
            for flow_type in ['fund_flow_rank', 'main_fund_flow', 'fund_flow']:
                if flow_type in files_dict:
                    for file in files_dict[flow_type]:
                        filepath = os.path.join(self.date_dir, file)
                        try:
                            df = pd.read_csv(filepath, encoding='utf-8')
                            if not df.empty:
                                # 智能识别列名
                                name_col = None
                                net_col = None
                                
                                for col in ['名称', '股票简称', '代码']:
                                    if col in df.columns:
                                        name_col = col
                                        break
                                
                                for col in df.columns:
                                    if '净' in col and ('流入' in col or '额' in col):
                                        net_col = col
                                        break
                                
                                if name_col and net_col:
                                    df[net_col] = pd.to_numeric(df[net_col], errors='coerce').fillna(0)
                                    top10 = df.nlargest(10, net_col)
                                    
                                    for idx, row in top10.iterrows():
                                        name = row[name_col]
                                        net_flow = row[net_col]
                                        rank = idx + 1
                                        
                                        current_top10[name] = {
                                            'rank': rank,
                                            'net_flow': net_flow,
                                            'type': flow_type
                                        }
                                    break
                        except Exception as e:
                            continue
                    break
            
            # 计算排名变化
            if i > 0 and previous_top10 and current_top10:
                ranking_changes = []
                new_entries = []
                lost_entries = []
                
                for name, curr_data in current_top10.items():
                    if name in previous_top10:
                        prev_rank = previous_top10[name]['rank']
                        curr_rank = curr_data['rank']
                        if prev_rank != curr_rank:
                            ranking_changes.append({
                                'name': name,
                                'previous_rank': prev_rank,
                                'current_rank': curr_rank,
                                'change': curr_rank - prev_rank,
                                'net_flow': curr_data['net_flow']
                            })
                    else:
                        new_entries.append({
                            'name': name,
                            'rank': curr_data['rank'],
                            'net_flow': curr_data['net_flow']
                        })
                
                for name in previous_top10:
                    if name not in current_top10:
                        lost_entries.append({
                            'name': name,
                            'previous_rank': previous_top10[name]['rank'],
                            'net_flow': previous_top10[name]['net_flow']
                        })
                
                change_record = {
                    'time': time_key,
                    'ranking_changes': ranking_changes,
                    'new_entries': new_entries,
                    'lost_entries': lost_entries,
                    'total_changes': len(ranking_changes) + len(new_entries) + len(lost_entries)
                }
                flow_changes.append(change_record)
            
            previous_top10 = current_top10.copy()
        
        return flow_changes
    
    def analyze_concept_flow_dynamics(self):
        """分析概念板块资金流动态变化"""
        print("💡 分析概念板块资金流动态变化...")
        return self._analyze_board_flow_dynamics('concept_flow', '概念板块')
    
    def analyze_sector_flow_dynamics(self):
        """分析行业板块资金流动态变化"""
        print("🏭 分析行业板块资金流动态变化...")
        return self._analyze_board_flow_dynamics('sector_flow', '行业板块')
    
    def _analyze_board_flow_dynamics(self, flow_type, board_name):
        """通用板块资金流动态分析"""
        flow_changes = []
        previous_top10 = {}
        
        # 获取所有时间点
        flow_times = []
        for time_key, files_dict in self.time_series_data.items():
            if flow_type in files_dict and time_key != '全天数据':
                flow_times.append(time_key)
        
        flow_times.sort()
        
        for i, time_key in enumerate(flow_times):
            files_dict = self.time_series_data[time_key]
            current_top10 = {}
            
            # 加载当前时间段数据
            for file in files_dict[flow_type]:
                filepath = os.path.join(self.date_dir, file)
                try:
                    df = pd.read_csv(filepath, encoding='utf-8')
                    if not df.empty:
                        # 智能识别列名
                        name_col = None
                        net_col = None
                        
                        for col in ['名称', '行业', '板块名称', '概念名称']:
                            if col in df.columns:
                                name_col = col
                                break
                        
                        for col in df.columns:
                            if '净' in col and ('流入' in col or '额' in col):
                                net_col = col
                                break
                        
                        if name_col and net_col:
                            df[net_col] = pd.to_numeric(df[net_col], errors='coerce').fillna(0)
                            top10 = df.nlargest(10, net_col)
                            
                            for idx, row in top10.iterrows():
                                name = row[name_col]
                                net_flow = row[net_col]
                                
                                current_top10[name] = {
                                    'rank': idx + 1,
                                    'net_flow': net_flow
                                }
                            break
                except Exception as e:
                    continue
            
            # 计算变化
            if i > 0 and previous_top10 and current_top10:
                ranking_changes = []
                new_top10 = []
                lost_top10 = []
                
                for name, curr_data in current_top10.items():
                    if name in previous_top10:
                        prev_rank = previous_top10[name]['rank']
                        curr_rank = curr_data['rank']
                        if prev_rank != curr_rank:
                            ranking_changes.append({
                                'name': name,
                                'previous_rank': prev_rank,
                                'current_rank': curr_rank,
                                'change': curr_rank - prev_rank,
                                'net_flow': curr_data['net_flow']
                            })
                    else:
                        new_top10.append({
                            'name': name,
                            'rank': curr_data['rank'],
                            'net_flow': curr_data['net_flow']
                        })
                
                for name in previous_top10:
                    if name not in current_top10:
                        lost_top10.append({
                            'name': name,
                            'previous_rank': previous_top10[name]['rank'],
                            'net_flow': previous_top10[name]['net_flow']
                        })
                
                change_record = {
                    'time': time_key,
                    'board_type': board_name,
                    'ranking_changes': ranking_changes,
                    'new_top10': new_top10,
                    'lost_top10': lost_top10,
                    'total_changes': len(ranking_changes) + len(new_top10) + len(lost_top10)
                }
                flow_changes.append(change_record)
            
            previous_top10 = current_top10.copy()
        
        return flow_changes

    def analyze_movers_dynamics(self):
        """分析异动股票动态变化"""
        print("🚀 分析异动股票动态变化...")

        movers_changes = []
        previous_movers = {}

        # 获取所有异动数据时间点
        movers_times = []
        for time_key, files_dict in self.time_series_data.items():
            if 'movers' in files_dict and time_key != '全天数据':
                movers_times.append(time_key)

        movers_times.sort()

        for i, time_key in enumerate(movers_times):
            files_dict = self.time_series_data[time_key]
            current_movers = {}

            # 加载异动数据
            for file in files_dict['movers']:
                filepath = os.path.join(self.date_dir, file)
                try:
                    df = pd.read_csv(filepath, encoding='utf-8')
                    if not df.empty:
                        # 按异动类型分类
                        if '异动类型' in df.columns or '类型' in df.columns:
                            type_col = '异动类型' if '异动类型' in df.columns else '类型'
                            name_col = '名称' if '名称' in df.columns else '股票简称'

                            if name_col in df.columns:
                                for mover_type in df[type_col].unique():
                                    type_stocks = df[df[type_col] == mover_type][name_col].tolist()
                                    current_movers[mover_type] = type_stocks
                except Exception as e:
                    continue

            # 计算变化
            if i > 0 and previous_movers:
                type_changes = {}

                all_types = set(current_movers.keys()) | set(previous_movers.keys())
                for mover_type in all_types:
                    curr_stocks = set(current_movers.get(mover_type, []))
                    prev_stocks = set(previous_movers.get(mover_type, []))

                    new_stocks = curr_stocks - prev_stocks
                    lost_stocks = prev_stocks - curr_stocks

                    if new_stocks or lost_stocks:
                        type_changes[mover_type] = {
                            'new_stocks': list(new_stocks),
                            'lost_stocks': list(lost_stocks),
                            'total_change': len(curr_stocks) - len(prev_stocks)
                        }

                if type_changes:
                    change_record = {
                        'time': time_key,
                        'type_changes': type_changes,
                        'total_types': len(current_movers)
                    }
                    movers_changes.append(change_record)

            previous_movers = current_movers.copy()

        return movers_changes

    def analyze_news_dynamics(self):
        """分析新闻动态变化"""
        print("📰 分析新闻动态变化...")

        news_changes = []
        previous_news_count = 0

        # 获取所有新闻时间点
        news_times = []
        for time_key, files_dict in self.time_series_data.items():
            if 'news' in files_dict and time_key != '全天数据':
                news_times.append(time_key)

        news_times.sort()

        for i, time_key in enumerate(news_times):
            files_dict = self.time_series_data[time_key]
            current_news_count = 0
            news_titles = []

            # 统计新闻数量
            for file in files_dict['news']:
                filepath = os.path.join(self.date_dir, file)
                try:
                    df = pd.read_csv(filepath, encoding='utf-8')
                    current_news_count += len(df)

                    # 提取新闻标题
                    if '标题' in df.columns:
                        news_titles.extend(df['标题'].tolist())
                    elif '新闻标题' in df.columns:
                        news_titles.extend(df['新闻标题'].tolist())
                except Exception as e:
                    continue

            # 计算变化
            if i > 0:
                news_change = current_news_count - previous_news_count

                if news_change != 0:
                    change_record = {
                        'time': time_key,
                        'current_count': current_news_count,
                        'previous_count': previous_news_count,
                        'change': news_change,
                        'sample_titles': news_titles[:3] if news_titles else []
                    }
                    news_changes.append(change_record)

            previous_news_count = current_news_count

        return news_changes

    def analyze_index_dynamics(self):
        """分析指数动态变化"""
        print("📊 分析指数动态变化...")

        index_changes = []
        previous_index_data = {}

        # 获取所有指数时间点
        index_times = []
        for time_key, files_dict in self.time_series_data.items():
            if 'index' in files_dict and time_key != '全天数据':
                index_times.append(time_key)

        index_times.sort()

        for i, time_key in enumerate(index_times):
            files_dict = self.time_series_data[time_key]
            current_index_data = {}

            # 加载指数数据
            for file in files_dict['index']:
                filepath = os.path.join(self.date_dir, file)
                try:
                    df = pd.read_csv(filepath, encoding='utf-8')
                    if not df.empty:
                        # 提取主要指数信息
                        if '指数名称' in df.columns and '最新价' in df.columns:
                            for _, row in df.iterrows():
                                index_name = row['指数名称']
                                current_price = row['最新价']
                                change_pct = row.get('涨跌幅', 0)

                                current_index_data[index_name] = {
                                    'price': current_price,
                                    'change_pct': change_pct
                                }
                except Exception as e:
                    continue

            # 计算变化
            if i > 0 and previous_index_data:
                significant_changes = []

                for index_name, curr_data in current_index_data.items():
                    if index_name in previous_index_data:
                        prev_pct = previous_index_data[index_name]['change_pct']
                        curr_pct = curr_data['change_pct']
                        pct_change = curr_pct - prev_pct

                        # 只记录显著变化（>0.5%）
                        if abs(pct_change) > 0.5:
                            significant_changes.append({
                                'index_name': index_name,
                                'previous_pct': prev_pct,
                                'current_pct': curr_pct,
                                'pct_change': pct_change
                            })

                if significant_changes:
                    change_record = {
                        'time': time_key,
                        'significant_changes': significant_changes,
                        'total_indices': len(current_index_data)
                    }
                    index_changes.append(change_record)

            previous_index_data = current_index_data.copy()

        return index_changes

    def analyze_big_deal_dynamics(self):
        """分析大宗交易动态变化"""
        print("💎 分析大宗交易动态变化...")

        big_deal_changes = []
        previous_deal_count = 0
        previous_total_amount = 0

        # 获取所有大宗交易时间点
        deal_times = []
        for time_key, files_dict in self.time_series_data.items():
            if 'big_deal' in files_dict and time_key != '全天数据':
                deal_times.append(time_key)

        deal_times.sort()

        for i, time_key in enumerate(deal_times):
            files_dict = self.time_series_data[time_key]
            current_deal_count = 0
            current_total_amount = 0

            # 统计大宗交易
            for file in files_dict['big_deal']:
                filepath = os.path.join(self.date_dir, file)
                try:
                    df = pd.read_csv(filepath, encoding='utf-8')
                    current_deal_count += len(df)

                    # 计算总金额
                    amount_col = None
                    for col in df.columns:
                        if '成交额' in col or '金额' in col:
                            amount_col = col
                            break

                    if amount_col:
                        df[amount_col] = pd.to_numeric(df[amount_col], errors='coerce').fillna(0)
                        current_total_amount += df[amount_col].sum()
                except Exception as e:
                    continue

            # 计算变化
            if i > 0:
                count_change = current_deal_count - previous_deal_count
                amount_change = current_total_amount - previous_total_amount

                if count_change != 0 or amount_change != 0:
                    change_record = {
                        'time': time_key,
                        'current_count': current_deal_count,
                        'count_change': count_change,
                        'current_amount': current_total_amount,
                        'amount_change': amount_change
                    }
                    big_deal_changes.append(change_record)

            previous_deal_count = current_deal_count
            previous_total_amount = current_total_amount

        return big_deal_changes

    def generate_comprehensive_dynamic_report(self, results):
        """生成综合动态变化报告"""
        print("\n📝 生成综合动态变化报告...")

        report = f"""
# {self.analysis_date} 全面动态变化分析报告

## 📊 报告概览

本报告详细分析了{self.analysis_date}交易日所有数据类型的动态变化情况，包括：
- 🎯 涨停池动态变化
- 💰 资金流向排名变化
- 💡 概念板块资金流变化
- 🏭 行业板块资金流变化
- 🚀 异动股票变化
- 📰 新闻动态变化
- 📊 指数变化
- 💎 大宗交易变化

---

## 🎯 涨停池动态变化分析

"""

        # 涨停池变化分析
        zt_changes = results['zt_pool_changes']
        if zt_changes:
            report += f"### 📈 涨停池变化统计\n\n"
            report += f"- **分析时间段**: {len(zt_changes)} 个\n"

            total_new = sum(change['new_count'] for change in zt_changes)
            total_lost = sum(change['lost_count'] for change in zt_changes)
            max_change = max(zt_changes, key=lambda x: abs(x['total_change']))

            report += f"- **总新增次数**: {total_new}\n"
            report += f"- **总失去次数**: {total_lost}\n"
            report += f"- **最大变化**: {max_change['time']} ({max_change['total_change']:+d}只)\n\n"

            report += "### 🔄 详细变化记录\n\n"

            for change in zt_changes[:20]:  # 显示前20个变化
                time_key = change['time']
                total_change = change['total_change']
                new_stocks = change['new_stocks']
                lost_stocks = change['lost_stocks']

                report += f"**⏰ {time_key}** | 📊 {change['total_stocks']}只 | 📈 {total_change:+d}\n\n"

                if new_stocks:
                    report += f"✅ **新增涨停** ({len(new_stocks)}只):\n"
                    for stock in new_stocks[:5]:  # 最多显示5只
                        if stock in change['current_data']:
                            data = change['current_data'][stock]
                            report += f"  - {stock} | {data['industry']} | {data['board_count']}板\n"
                    if len(new_stocks) > 5:
                        report += f"  - ... 还有 {len(new_stocks)-5} 只\n"
                    report += "\n"

                if lost_stocks:
                    report += f"❌ **失去涨停** ({len(lost_stocks)}只):\n"
                    for stock in lost_stocks[:5]:  # 最多显示5只
                        if stock in change['previous_data']:
                            data = change['previous_data'][stock]
                            report += f"  - {stock} | {data['industry']} | {data['board_count']}板\n"
                    if len(lost_stocks) > 5:
                        report += f"  - ... 还有 {len(lost_stocks)-5} 只\n"
                    report += "\n"

                # 行业变化
                if change['industry_changes']:
                    report += f"🏭 **行业变化**:\n"
                    for industry, change_data in change['industry_changes'].items():
                        prev_count = change_data['previous']
                        curr_count = change_data['current']
                        change_val = change_data['change']
                        report += f"  - {industry}: {prev_count}只 → {curr_count}只 ({change_val:+d})\n"
                    report += "\n"

                report += "---\n\n"
        else:
            report += "❌ 未发现涨停池变化数据\n\n"

        # 资金流向变化分析
        report += "\n## 💰 资金流向排名动态变化\n\n"

        fund_flow_changes = results['fund_flow_changes']
        if fund_flow_changes:
            report += f"### 📊 资金流向变化统计\n\n"
            report += f"- **变化时间段**: {len(fund_flow_changes)} 个\n"

            total_changes = sum(change['total_changes'] for change in fund_flow_changes)
            report += f"- **总变化次数**: {total_changes}\n\n"

            report += "### 🔄 排名变化详情\n\n"

            for change in fund_flow_changes[:15]:  # 显示前15个变化
                time_key = change['time']
                ranking_changes = change['ranking_changes']
                new_entries = change['new_entries']
                lost_entries = change['lost_entries']

                if ranking_changes or new_entries or lost_entries:
                    report += f"**⏰ {time_key}**\n\n"

                    if ranking_changes:
                        report += f"📈 **排名变化** ({len(ranking_changes)}个):\n"
                        for rank_change in ranking_changes[:5]:
                            name = rank_change['name']
                            prev_rank = rank_change['previous_rank']
                            curr_rank = rank_change['current_rank']
                            net_flow = rank_change['net_flow']
                            report += f"  - {name}: {prev_rank}位 → {curr_rank}位 | {net_flow:.0f}万\n"
                        if len(ranking_changes) > 5:
                            report += f"  - ... 还有 {len(ranking_changes)-5} 个变化\n"
                        report += "\n"

                    if new_entries:
                        report += f"✅ **新进前10** ({len(new_entries)}个):\n"
                        for entry in new_entries[:3]:
                            name = entry['name']
                            rank = entry['rank']
                            net_flow = entry['net_flow']
                            report += f"  - {name}: 第{rank}位 | {net_flow:.0f}万\n"
                        if len(new_entries) > 3:
                            report += f"  - ... 还有 {len(new_entries)-3} 个\n"
                        report += "\n"

                    if lost_entries:
                        report += f"❌ **跌出前10** ({len(lost_entries)}个):\n"
                        for entry in lost_entries[:3]:
                            name = entry['name']
                            prev_rank = entry['previous_rank']
                            net_flow = entry['net_flow']
                            report += f"  - {name}: 原第{prev_rank}位 | {net_flow:.0f}万\n"
                        if len(lost_entries) > 3:
                            report += f"  - ... 还有 {len(lost_entries)-3} 个\n"
                        report += "\n"

                    report += "---\n\n"
        else:
            report += "❌ 未发现资金流向变化数据\n\n"

        # 概念板块变化分析
        report += "\n## 💡 概念板块资金流动态变化\n\n"

        concept_changes = results['concept_flow_changes']
        if concept_changes:
            report += self._generate_board_flow_report(concept_changes, "概念板块")
        else:
            report += "❌ 未发现概念板块变化数据\n\n"

        # 行业板块变化分析
        report += "\n## 🏭 行业板块资金流动态变化\n\n"

        sector_changes = results['sector_flow_changes']
        if sector_changes:
            report += self._generate_board_flow_report(sector_changes, "行业板块")
        else:
            report += "❌ 未发现行业板块变化数据\n\n"

        # 异动股票变化分析
        report += "\n## 🚀 异动股票动态变化\n\n"

        movers_changes = results['movers_changes']
        if movers_changes:
            report += f"### 📊 异动股票变化统计\n\n"
            report += f"- **变化时间段**: {len(movers_changes)} 个\n\n"

            report += "### 🔄 异动类型变化详情\n\n"

            for change in movers_changes[:10]:  # 显示前10个变化
                time_key = change['time']
                type_changes = change['type_changes']

                report += f"**⏰ {time_key}**\n\n"

                for mover_type, type_data in type_changes.items():
                    new_stocks = type_data['new_stocks']
                    lost_stocks = type_data['lost_stocks']
                    total_change = type_data['total_change']

                    report += f"📈 **{mover_type}** ({total_change:+d}):\n"

                    if new_stocks:
                        report += f"  ✅ 新增: {', '.join(new_stocks[:3])}"
                        if len(new_stocks) > 3:
                            report += f" ... 还有{len(new_stocks)-3}只"
                        report += "\n"

                    if lost_stocks:
                        report += f"  ❌ 失去: {', '.join(lost_stocks[:3])}"
                        if len(lost_stocks) > 3:
                            report += f" ... 还有{len(lost_stocks)-3}只"
                        report += "\n"

                    report += "\n"

                report += "---\n\n"
        else:
            report += "❌ 未发现异动股票变化数据\n\n"

        # 新闻变化分析
        report += "\n## 📰 新闻动态变化\n\n"

        news_changes = results['news_changes']
        if news_changes:
            report += f"### 📊 新闻变化统计\n\n"
            report += f"- **变化时间段**: {len(news_changes)} 个\n\n"

            for change in news_changes[:10]:
                time_key = change['time']
                current_count = change['current_count']
                change_val = change['change']
                sample_titles = change['sample_titles']

                report += f"**⏰ {time_key}** | 📰 {current_count}条 | 📈 {change_val:+d}\n"

                if sample_titles:
                    report += f"  📝 示例新闻:\n"
                    for title in sample_titles:
                        report += f"    - {title[:50]}...\n"

                report += "\n"
        else:
            report += "❌ 未发现新闻变化数据\n\n"

        # 指数变化分析
        report += "\n## 📊 指数动态变化\n\n"

        index_changes = results['index_changes']
        if index_changes:
            report += f"### 📈 指数变化统计\n\n"
            report += f"- **变化时间段**: {len(index_changes)} 个\n\n"

            for change in index_changes[:10]:
                time_key = change['time']
                significant_changes = change['significant_changes']

                report += f"**⏰ {time_key}**\n\n"

                for idx_change in significant_changes:
                    index_name = idx_change['index_name']
                    prev_pct = idx_change['previous_pct']
                    curr_pct = idx_change['current_pct']
                    pct_change = idx_change['pct_change']

                    direction = "📈" if pct_change > 0 else "📉"
                    report += f"  {direction} {index_name}: {prev_pct:.2f}% → {curr_pct:.2f}% ({pct_change:+.2f}%)\n"

                report += "\n---\n\n"
        else:
            report += "❌ 未发现指数变化数据\n\n"

        # 大宗交易变化分析
        report += "\n## 💎 大宗交易动态变化\n\n"

        big_deal_changes = results['big_deal_changes']
        if big_deal_changes:
            report += f"### 💰 大宗交易变化统计\n\n"
            report += f"- **变化时间段**: {len(big_deal_changes)} 个\n\n"

            for change in big_deal_changes[:10]:
                time_key = change['time']
                current_count = change['current_count']
                count_change = change['count_change']
                current_amount = change['current_amount']
                amount_change = change['amount_change']

                report += f"**⏰ {time_key}**\n"
                report += f"  📊 交易笔数: {current_count}笔 ({count_change:+d})\n"
                report += f"  💰 交易金额: {current_amount:.0f}万 ({amount_change:+.0f}万)\n\n"
        else:
            report += "❌ 未发现大宗交易变化数据\n\n"

        # 总结
        report += "\n## 📋 动态变化总结\n\n"

        total_zt_changes = len(results['zt_pool_changes'])
        total_flow_changes = len(results['fund_flow_changes'])
        total_concept_changes = len(results['concept_flow_changes'])
        total_sector_changes = len(results['sector_flow_changes'])
        total_movers_changes = len(results['movers_changes'])
        total_news_changes = len(results['news_changes'])
        total_index_changes = len(results['index_changes'])
        total_deal_changes = len(results['big_deal_changes'])

        report += f"### 📊 变化统计汇总\n\n"
        report += f"| 数据类型 | 变化时间段 | 状态 |\n"
        report += f"|---------|-----------|------|\n"
        report += f"| 🎯 涨停池 | {total_zt_changes} | {'✅' if total_zt_changes > 0 else '❌'} |\n"
        report += f"| 💰 资金流向 | {total_flow_changes} | {'✅' if total_flow_changes > 0 else '❌'} |\n"
        report += f"| 💡 概念板块 | {total_concept_changes} | {'✅' if total_concept_changes > 0 else '❌'} |\n"
        report += f"| 🏭 行业板块 | {total_sector_changes} | {'✅' if total_sector_changes > 0 else '❌'} |\n"
        report += f"| 🚀 异动股票 | {total_movers_changes} | {'✅' if total_movers_changes > 0 else '❌'} |\n"
        report += f"| 📰 新闻 | {total_news_changes} | {'✅' if total_news_changes > 0 else '❌'} |\n"
        report += f"| 📊 指数 | {total_index_changes} | {'✅' if total_index_changes > 0 else '❌'} |\n"
        report += f"| 💎 大宗交易 | {total_deal_changes} | {'✅' if total_deal_changes > 0 else '❌'} |\n\n"

        active_types = sum([
            1 if total_zt_changes > 0 else 0,
            1 if total_flow_changes > 0 else 0,
            1 if total_concept_changes > 0 else 0,
            1 if total_sector_changes > 0 else 0,
            1 if total_movers_changes > 0 else 0,
            1 if total_news_changes > 0 else 0,
            1 if total_index_changes > 0 else 0,
            1 if total_deal_changes > 0 else 0
        ])

        report += f"### 🎯 动态活跃度评估\n\n"
        report += f"- **活跃数据类型**: {active_types}/8 个\n"
        report += f"- **活跃度评分**: {active_types/8*100:.1f}%\n"

        if active_types >= 6:
            report += f"- **评估结果**: 🟢 高度活跃 - 市场动态变化丰富\n"
        elif active_types >= 4:
            report += f"- **评估结果**: 🟡 中等活跃 - 市场有一定动态变化\n"
        else:
            report += f"- **评估结果**: 🔴 低度活跃 - 市场动态变化较少\n"

        report += f"\n---\n"
        report += f"*报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}*\n"

        return report

    def _generate_board_flow_report(self, changes, board_type):
        """生成板块资金流变化报告"""
        report = f"### 📊 {board_type}变化统计\n\n"
        report += f"- **变化时间段**: {len(changes)} 个\n\n"

        report += "### 🔄 排名变化详情\n\n"

        for change in changes[:15]:  # 显示前15个变化
            time_key = change['time']
            ranking_changes = change['ranking_changes']
            new_top10 = change['new_top10']
            lost_top10 = change['lost_top10']

            if ranking_changes or new_top10 or lost_top10:
                report += f"**⏰ {time_key}**\n\n"

                if ranking_changes:
                    report += f"📈 **排名变化** ({len(ranking_changes)}个):\n"
                    for rank_change in ranking_changes[:5]:
                        name = rank_change['name']
                        prev_rank = rank_change['previous_rank']
                        curr_rank = rank_change['current_rank']
                        net_flow = rank_change['net_flow']
                        report += f"  - {name}: {prev_rank}位 → {curr_rank}位 | {net_flow:.0f}万\n"
                    if len(ranking_changes) > 5:
                        report += f"  - ... 还有 {len(ranking_changes)-5} 个变化\n"
                    report += "\n"

                if new_top10:
                    report += f"✅ **新进前10** ({len(new_top10)}个):\n"
                    for entry in new_top10[:3]:
                        name = entry['name']
                        rank = entry['rank']
                        net_flow = entry['net_flow']
                        report += f"  - {name}: 第{rank}位 | {net_flow:.0f}万\n"
                    if len(new_top10) > 3:
                        report += f"  - ... 还有 {len(new_top10)-3} 个\n"
                    report += "\n"

                if lost_top10:
                    report += f"❌ **跌出前10** ({len(lost_top10)}个):\n"
                    for entry in lost_top10[:3]:
                        name = entry['name']
                        prev_rank = entry['previous_rank']
                        net_flow = entry['net_flow']
                        report += f"  - {name}: 原第{prev_rank}位 | {net_flow:.0f}万\n"
                    if len(lost_top10) > 3:
                        report += f"  - ... 还有 {len(lost_top10)-3} 个\n"
                    report += "\n"

                report += "---\n\n"

        return report

    def save_dynamic_report(self, report):
        """保存动态分析报告"""
        os.makedirs("../reports", exist_ok=True)

        report_path = f"../reports/enhanced_dynamic_analysis_{self.analysis_date}.md"

        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"✅ 增强版动态分析报告已保存到: {report_path}")
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")

def main():
    """主函数"""
    analysis_date = "2025-07-25"
    data_dir = "../fund_data"

    print("🚀 启动增强版动态分析器...")
    print("=" * 80)

    # 创建分析器
    analyzer = EnhancedDynamicAnalyzer(analysis_date, data_dir)

    # 执行全面动态分析
    results = analyzer.analyze_all_dynamic_changes()

    print("\n" + "=" * 80)
    print("✅ 增强版动态分析完成！")

    # 显示简要统计
    print(f"\n📊 分析结果统计:")
    print(f"  🎯 涨停池变化: {len(results['zt_pool_changes'])} 个时间段")
    print(f"  💰 资金流向变化: {len(results['fund_flow_changes'])} 个时间段")
    print(f"  💡 概念板块变化: {len(results['concept_flow_changes'])} 个时间段")
    print(f"  🏭 行业板块变化: {len(results['sector_flow_changes'])} 个时间段")
    print(f"  🚀 异动股票变化: {len(results['movers_changes'])} 个时间段")
    print(f"  📰 新闻变化: {len(results['news_changes'])} 个时间段")
    print(f"  📊 指数变化: {len(results['index_changes'])} 个时间段")
    print(f"  💎 大宗交易变化: {len(results['big_deal_changes'])} 个时间段")

if __name__ == "__main__":
    main()
