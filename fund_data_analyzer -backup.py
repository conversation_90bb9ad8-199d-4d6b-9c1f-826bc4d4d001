#!/usr/bin/.env python3
# -*- coding: utf-8 -*-
"""
基金数据时间序列分析器
按时间段分析fund_data文件夹中的数据，生成一天的详细报告
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import glob
import re
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# 配置参数
ANALYSIS_DATE = "2025-07-16"  # 要分析的日期，可在此修改
FUND_DATA_DIR = "../fund_data"
OUTPUT_DIR = "../reports"

class FundDataAnalyzer:
    def __init__(self, analysis_date, data_dir):
        self.analysis_date = analysis_date
        self.data_dir = data_dir
        self.date_dir = os.path.join(data_dir, analysis_date)
        self.time_data = defaultdict(dict)
        self.file_types = {
            'fund_flow': ['fund_flow_tpdog.csv', 'ths_fund_flow.csv', 'fund_flow_akshare.csv'],
            'concept_flow': ['concept_fund_flow_tpdog.csv', 'concept_fund_flow_akshare.csv'],
            'sector_flow': ['sector_fund_flow_tpdog.csv', 'sector_fund_flow_akshare.csv'],
            'zt_pool': ['zt_pool.csv', 'previous_zt_pool.csv'],
            'news': ['news_cls.csv', 'news_em.csv', 'news_ths.csv'],
            'index': ['index_sh000001.csv', 'index_sz399006.csv'],
            'movers': ['movers_大笔买入.csv', 'movers_有大买盘.csv'],
            'big_deal': ['ths_big_deal.csv'],
            'board_changes': ['board_changes.csv'],
            'market_flow': ['market_fund_flow.csv'],
            'industry_board': ['industry_board_ths.csv', 'industry_board_em.csv'],
            'indicator': ['indicator_创月新高.csv'],
            'individual_flow': ['individual_fund_flow_'],
            'lhb': ['lhb_jgmmtj.csv'],
            'dzjy': ['dzjy_mrmx.csv'],
            'notices': ['stock_notices.txt']
        }
        
    def scan_files(self):
        """扫描指定日期文件夹中的所有文件"""
        if not os.path.exists(self.date_dir):
            print(f"错误：日期文件夹 {self.date_dir} 不存在")
            return False
            
        files = os.listdir(self.date_dir)
        print(f"发现 {len(files)} 个文件")
        
        # 按时间分组文件
        for file in files:
            if file.endswith('.csv') or file.endswith('.txt'):
                # 提取时间信息 (格式: HH-MM)
                time_match = re.search(r'(\d{2})-(\d{2})_', file)
                if time_match:
                    hour, minute = time_match.groups()
                    time_key = f"{hour}:{minute}"
                    
                    if time_key not in self.time_data:
                        self.time_data[time_key] = {}
                    
                    # 分类文件
                    file_type = self.classify_file(file)
                    if file_type not in self.time_data[time_key]:
                        self.time_data[time_key][file_type] = []
                    self.time_data[time_key][file_type].append(file)
        
        print(f"按时间分组后，共有 {len(self.time_data)} 个时间点")
        return True
    
    def classify_file(self, filename):
        """根据文件名分类文件类型"""
        # 按照模式长度排序，优先匹配更具体的模式
        all_patterns = []
        for file_type, patterns in self.file_types.items():
            for pattern in patterns:
                all_patterns.append((pattern, file_type))

        # 按模式长度降序排序，优先匹配更长的模式
        all_patterns.sort(key=lambda x: len(x[0]), reverse=True)

        for pattern, file_type in all_patterns:
            if pattern in filename:
                return file_type

        # 特殊处理
        if 'individual_fund_flow' in filename:
            return 'individual_flow'
        elif 'indicator' in filename:
            return 'indicator'
        elif 'industry_board' in filename:
            return 'industry_board'
        elif 'stock_notices' in filename:
            return 'notices'
        else:
            return 'other'
    
    def load_csv_safe(self, filepath):
        """安全加载CSV文件"""
        try:
            # 尝试不同的编码
            for encoding in ['utf-8', 'gbk', 'gb2312']:
                try:
                    df = pd.read_csv(filepath, encoding=encoding)
                    return df
                except UnicodeDecodeError:
                    continue
            return None
        except Exception as e:
            print(f"加载文件失败 {filepath}: {e}")
            return None
    
    def analyze_time_period(self, time_key, files_dict):
        """分析特定时间段的数据"""
        analysis = {
            'time': time_key,
            'summary': {},
            'details': {},
            'changes': {}
        }
        
        # 分析资金流向
        if 'fund_flow' in files_dict:
            fund_flow_analysis = self.analyze_fund_flow(time_key, files_dict['fund_flow'])
            analysis['details']['fund_flow'] = fund_flow_analysis
        
        # 分析涨停池
        if 'zt_pool' in files_dict:
            zt_analysis = self.analyze_zt_pool(time_key, files_dict['zt_pool'])
            analysis['details']['zt_pool'] = zt_analysis
        
        # 分析新闻
        if 'news' in files_dict:
            news_analysis = self.analyze_news(time_key, files_dict['news'])
            analysis['details']['news'] = news_analysis
        
        # 分析概念板块资金流向
        if 'concept_flow' in files_dict:
            concept_analysis = self.analyze_concept_flow(time_key, files_dict['concept_flow'])
            analysis['details']['concept_flow'] = concept_analysis

        # 分析行业板块资金流向
        if 'sector_flow' in files_dict:
            sector_analysis = self.analyze_sector_flow(time_key, files_dict['sector_flow'])
            analysis['details']['sector_flow'] = sector_analysis

        # 分析异动股票
        if 'movers' in files_dict:
            movers_analysis = self.analyze_movers(time_key, files_dict['movers'])
            analysis['details']['movers'] = movers_analysis

        return analysis
    
    def analyze_fund_flow(self, time_key, files):
        """分析资金流向数据"""
        analysis = {'top_inflow': [], 'top_outflow': [], 'total_stats': {}}
        
        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                # 寻找净流入相关列
                net_flow_cols = [col for col in df.columns if '净' in col and ('流入' in col or '额' in col)]
                if net_flow_cols:
                    net_col = net_flow_cols[0]
                    df[net_col] = pd.to_numeric(df[net_col], errors='coerce')
                    
                    # 获取前10大流入和流出
                    top_inflow = df.nlargest(5, net_col)[['代码', '名称', net_col]].to_dict('records') if '代码' in df.columns else []
                    top_outflow = df.nsmallest(5, net_col)[['代码', '名称', net_col]].to_dict('records') if '代码' in df.columns else []
                    
                    analysis['top_inflow'].extend(top_inflow)
                    analysis['top_outflow'].extend(top_outflow)
                    
                    # 统计信息
                    analysis['total_stats'][file] = {
                        'total_stocks': len(df),
                        'positive_flow': len(df[df[net_col] > 0]),
                        'negative_flow': len(df[df[net_col] < 0]),
                        'total_net_flow': df[net_col].sum()
                    }
        
        return analysis
    
    def analyze_zt_pool(self, time_key, files):
        """分析涨停池数据"""
        analysis = {'zt_stocks': [], 'stats': {}}
        
        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                # 涨停股票信息
                zt_info = []
                for _, row in df.iterrows():
                    stock_info = {
                        'code': row.get('代码', ''),
                        'name': row.get('名称', ''),
                        'price': row.get('最新价', 0),
                        'change_pct': row.get('涨跌幅', 0),
                        'volume': row.get('成交额', 0),
                        'industry': row.get('所属行业', ''),
                        'board_count': row.get('连板数', 1)
                    }
                    zt_info.append(stock_info)
                
                analysis['zt_stocks'] = zt_info
                analysis['stats'] = {
                    'total_zt': len(df),
                    'industries': df['所属行业'].value_counts().to_dict() if '所属行业' in df.columns else {},
                    'avg_board_count': df['连板数'].mean() if '连板数' in df.columns else 0
                }
        
        return analysis
    
    def analyze_news(self, time_key, files):
        """分析新闻数据"""
        analysis = {'news_list': [], 'keywords': {}}

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                for _, row in df.iterrows():
                    title = row.get('标题', '')
                    content = row.get('内容', '')
                    time = row.get('发布时间', '')

                    # 处理NaN值
                    if pd.isna(title):
                        title = ''
                    if pd.isna(content):
                        content = ''
                    if pd.isna(time):
                        time = ''

                    news_item = {
                        'title': str(title),
                        'content': str(content),
                        'time': str(time),
                        'source': file.replace('.csv', '').split('_')[-1]
                    }
                    analysis['news_list'].append(news_item)

        return analysis

    def analyze_concept_flow(self, time_key, files):
        """分析概念板块资金流向数据"""
        analysis = {'top_inflow': [], 'top_outflow': [], 'total_stats': {}}

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                # 寻找净流入相关列
                net_flow_cols = [col for col in df.columns if '净' in col and ('流入' in col or '额' in col)]
                if net_flow_cols:
                    net_col = net_flow_cols[0]
                    df[net_col] = pd.to_numeric(df[net_col], errors='coerce')

                    # 获取前10大流入和流出概念
                    name_col = '名称' if '名称' in df.columns else 'code'
                    if name_col in df.columns:
                        top_inflow = df.nlargest(10, net_col)[[name_col, net_col]].to_dict('records')
                        top_outflow = df.nsmallest(10, net_col)[[name_col, net_col]].to_dict('records')

                        analysis['top_inflow'].extend(top_inflow)
                        analysis['top_outflow'].extend(top_outflow)

                        # 统计信息
                        analysis['total_stats'][file] = {
                            'total_concepts': len(df),
                            'positive_flow': len(df[df[net_col] > 0]),
                            'negative_flow': len(df[df[net_col] < 0]),
                            'total_net_flow': df[net_col].sum()
                        }

        return analysis

    def analyze_sector_flow(self, time_key, files):
        """分析行业板块资金流向数据"""
        analysis = {'top_inflow': [], 'top_outflow': [], 'total_stats': {}}

        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                # 寻找净流入相关列
                net_flow_cols = [col for col in df.columns if '净' in col and ('流入' in col or '额' in col)]
                if net_flow_cols:
                    net_col = net_flow_cols[0]
                    df[net_col] = pd.to_numeric(df[net_col], errors='coerce')

                    # 获取前10大流入和流出行业
                    name_col = '名称' if '名称' in df.columns else 'code'
                    if name_col in df.columns:
                        top_inflow = df.nlargest(10, net_col)[[name_col, net_col]].to_dict('records')
                        top_outflow = df.nsmallest(10, net_col)[[name_col, net_col]].to_dict('records')

                        analysis['top_inflow'].extend(top_inflow)
                        analysis['top_outflow'].extend(top_outflow)

                        # 统计信息
                        analysis['total_stats'][file] = {
                            'total_sectors': len(df),
                            'positive_flow': len(df[df[net_col] > 0]),
                            'negative_flow': len(df[df[net_col] < 0]),
                            'total_net_flow': df[net_col].sum()
                        }

        return analysis

    def analyze_movers(self, time_key, files):
        """分析异动股票"""
        analysis = {'movers': []}
        
        for file in files:
            filepath = os.path.join(self.date_dir, file)
            df = self.load_csv_safe(filepath)
            if df is not None and not df.empty:
                mover_type = file.split('_')[-1].replace('.csv', '')
                for _, row in df.iterrows():
                    mover_info = {
                        'type': mover_type,
                        'code': row.get('代码', ''),
                        'name': row.get('名称', ''),
                        'price': row.get('最新价', 0),
                        'change_pct': row.get('涨跌幅', 0)
                    }
                    analysis['movers'].append(mover_info)
        
        return analysis

    def compare_time_periods(self, current_analysis, previous_analysis):
        """对比两个时间段的变化 - 深度分析版"""
        changes = {}

        # 深度对比涨停池变化
        if 'zt_pool' in current_analysis['details'] and 'zt_pool' in previous_analysis['details']:
            changes['zt_changes'] = self.deep_compare_zt_pool(
                current_analysis['details']['zt_pool'],
                previous_analysis['details']['zt_pool']
            )

        # 深度对比资金流向变化
        if 'fund_flow' in current_analysis['details'] and 'fund_flow' in previous_analysis['details']:
            changes['flow_changes'] = self.deep_compare_fund_flow(
                current_analysis['details']['fund_flow'],
                previous_analysis['details']['fund_flow']
            )

        # 对比概念板块变化
        if 'concept_flow' in current_analysis['details'] and 'concept_flow' in previous_analysis['details']:
            changes['concept_changes'] = self.compare_concept_flow(
                current_analysis['details']['concept_flow'],
                previous_analysis['details']['concept_flow']
            )

        # 对比行业板块变化
        if 'sector_flow' in current_analysis['details'] and 'sector_flow' in previous_analysis['details']:
            changes['sector_changes'] = self.compare_sector_flow(
                current_analysis['details']['sector_flow'],
                previous_analysis['details']['sector_flow']
            )

        # 对比异动股票变化
        if 'movers' in current_analysis['details'] and 'movers' in previous_analysis['details']:
            changes['movers_changes'] = self.compare_movers(
                current_analysis['details']['movers'],
                previous_analysis['details']['movers']
            )

        return changes

    def deep_compare_zt_pool(self, current_zt, previous_zt):
        """深度对比涨停池变化"""
        current_stocks = {stock['code']: stock for stock in current_zt['zt_stocks']}
        previous_stocks = {stock['code']: stock for stock in previous_zt['zt_stocks']}

        current_codes = set(current_stocks.keys())
        previous_codes = set(previous_stocks.keys())

        # 基本变化
        new_zt = list(current_codes - previous_codes)
        lost_zt = list(previous_codes - current_codes)
        continued_zt = list(current_codes & previous_codes)

        # 详细分析新增涨停
        new_zt_details = []
        for code in new_zt:
            if code in current_stocks:
                stock = current_stocks[code]
                new_zt_details.append({
                    'code': code,
                    'name': stock.get('name', ''),
                    'industry': stock.get('industry', ''),
                    'board_count': stock.get('board_count', 1),
                    'volume': stock.get('volume', 0),
                    'change_pct': stock.get('change_pct', 0)
                })

        # 分析失去涨停的原因
        lost_zt_details = []
        for code in lost_zt:
            if code in previous_stocks:
                stock = previous_stocks[code]
                lost_zt_details.append({
                    'code': code,
                    'name': stock.get('name', ''),
                    'industry': stock.get('industry', ''),
                    'previous_board_count': stock.get('board_count', 1),
                    'previous_volume': stock.get('volume', 0)
                })

        # 分析连板数变化
        board_count_changes = []
        for code in continued_zt:
            if code in current_stocks and code in previous_stocks:
                current_board = current_stocks[code].get('board_count', 1)
                previous_board = previous_stocks[code].get('board_count', 1)
                if current_board != previous_board:
                    board_count_changes.append({
                        'code': code,
                        'name': current_stocks[code].get('name', ''),
                        'previous_board': previous_board,
                        'current_board': current_board,
                        'change': current_board - previous_board
                    })

        # 行业分析
        current_industries = {}
        previous_industries = {}

        for stock in current_zt['zt_stocks']:
            industry = stock.get('industry', '未知')
            current_industries[industry] = current_industries.get(industry, 0) + 1

        for stock in previous_zt['zt_stocks']:
            industry = stock.get('industry', '未知')
            previous_industries[industry] = previous_industries.get(industry, 0) + 1

        industry_changes = {}
        all_industries = set(current_industries.keys()) | set(previous_industries.keys())
        for industry in all_industries:
            current_count = current_industries.get(industry, 0)
            previous_count = previous_industries.get(industry, 0)
            if current_count != previous_count:
                industry_changes[industry] = {
                    'previous': previous_count,
                    'current': current_count,
                    'change': current_count - previous_count
                }

        return {
            'summary': {
                'total_change': len(current_codes) - len(previous_codes),
                'new_count': len(new_zt),
                'lost_count': len(lost_zt),
                'continued_count': len(continued_zt)
            },
            'new_zt_details': new_zt_details,
            'lost_zt_details': lost_zt_details,
            'board_count_changes': board_count_changes,
            'industry_changes': industry_changes
        }

    def deep_compare_fund_flow(self, current_flow, previous_flow):
        """深度对比资金流向变化"""
        changes = {}

        # 构建完整的股票资金流向字典
        current_stocks = {}
        previous_stocks = {}

        # 处理当前时段数据
        for stock in current_flow['top_inflow'] + current_flow['top_outflow']:
            if '代码' in stock:
                code = stock['代码']
                current_stocks[code] = {
                    'code': code,
                    'name': stock.get('名称', ''),
                    'net_flow': stock.get('今日主力净流入-净额', 0),
                    'flow_ratio': stock.get('今日主力净流入-净占比', 0),
                    'inflow': stock.get('今日主力净流入-总流入', 0),
                    'outflow': stock.get('今日主力净流入-总流出', 0)
                }

        # 处理之前时段数据
        for stock in previous_flow['top_inflow'] + previous_flow['top_outflow']:
            if '代码' in stock:
                code = stock['代码']
                previous_stocks[code] = {
                    'code': code,
                    'name': stock.get('名称', ''),
                    'net_flow': stock.get('今日主力净流入-净额', 0),
                    'flow_ratio': stock.get('今日主力净流入-净占比', 0),
                    'inflow': stock.get('今日主力净流入-总流入', 0),
                    'outflow': stock.get('今日主力净流入-总流出', 0)
                }

        # 分析排名变化
        current_top100 = set(list(current_stocks.keys())[:100])
        previous_top100 = set(list(previous_stocks.keys())[:100])

        # 新进入前100名
        new_top100 = list(current_top100 - previous_top100)
        # 跌出前100名
        lost_top100 = list(previous_top100 - current_top100)

        # 分析资金流向突然加大的股票
        flow_surge_stocks = []
        flow_drop_stocks = []

        for code in set(current_stocks.keys()) & set(previous_stocks.keys()):
            current_net = current_stocks[code]['net_flow']
            previous_net = previous_stocks[code]['net_flow']

            # 转换为数值类型
            try:
                current_net = float(current_net) if current_net else 0
                previous_net = float(previous_net) if previous_net else 0
            except:
                continue

            # 资金流向突然加大（增幅超过50%且绝对值超过1000万）
            if previous_net != 0:
                change_ratio = (current_net - previous_net) / abs(previous_net)
                if change_ratio > 0.5 and abs(current_net - previous_net) > 10000000:
                    flow_surge_stocks.append({
                        'code': code,
                        'name': current_stocks[code]['name'],
                        'previous_flow': previous_net,
                        'current_flow': current_net,
                        'change_amount': current_net - previous_net,
                        'change_ratio': change_ratio
                    })
                elif change_ratio < -0.5 and abs(current_net - previous_net) > 10000000:
                    flow_drop_stocks.append({
                        'code': code,
                        'name': current_stocks[code]['name'],
                        'previous_flow': previous_net,
                        'current_flow': current_net,
                        'change_amount': current_net - previous_net,
                        'change_ratio': change_ratio
                    })

        # 按变化幅度排序
        flow_surge_stocks.sort(key=lambda x: x['change_ratio'], reverse=True)
        flow_drop_stocks.sort(key=lambda x: x['change_ratio'])

        # 分析排名变化
        ranking_changes = []
        current_ranked = sorted(current_stocks.items(), key=lambda x: float(x[1]['net_flow']) if x[1]['net_flow'] else 0, reverse=True)
        previous_ranked = sorted(previous_stocks.items(), key=lambda x: float(x[1]['net_flow']) if x[1]['net_flow'] else 0, reverse=True)

        current_rank_dict = {code: idx+1 for idx, (code, _) in enumerate(current_ranked)}
        previous_rank_dict = {code: idx+1 for idx, (code, _) in enumerate(previous_ranked)}

        for code in set(current_rank_dict.keys()) & set(previous_rank_dict.keys()):
            current_rank = current_rank_dict[code]
            previous_rank = previous_rank_dict[code]
            rank_change = previous_rank - current_rank  # 正数表示排名上升

            if abs(rank_change) >= 20:  # 排名变化超过20位
                ranking_changes.append({
                    'code': code,
                    'name': current_stocks[code]['name'],
                    'previous_rank': previous_rank,
                    'current_rank': current_rank,
                    'rank_change': rank_change,
                    'current_flow': current_stocks[code]['net_flow']
                })

        ranking_changes.sort(key=lambda x: abs(x['rank_change']), reverse=True)

        return {
            'new_top100': [{'code': code, 'name': current_stocks.get(code, {}).get('name', ''),
                           'net_flow': current_stocks.get(code, {}).get('net_flow', 0)} for code in new_top100[:10]],
            'lost_top100': [{'code': code, 'name': previous_stocks.get(code, {}).get('name', ''),
                            'previous_flow': previous_stocks.get(code, {}).get('net_flow', 0)} for code in lost_top100[:10]],
            'flow_surge_stocks': flow_surge_stocks[:10],
            'flow_drop_stocks': flow_drop_stocks[:10],
            'ranking_changes': ranking_changes[:15]
        }

    def compare_concept_flow(self, current_concept, previous_concept):
        """对比概念板块资金流向变化"""
        changes = {}

        # 获取当前和之前的热门概念
        current_hot = set()
        previous_hot = set()

        if current_concept['top_inflow']:
            current_hot = set([concept.get('名称', concept.get('code', '')) for concept in current_concept['top_inflow'][:10]])
        if previous_concept['top_inflow']:
            previous_hot = set([concept.get('名称', concept.get('code', '')) for concept in previous_concept['top_inflow'][:10]])

        changes['hot_concepts'] = {
            'new_hot': list(current_hot - previous_hot),
            'lost_hot': list(previous_hot - current_hot),
            'continued_hot': list(current_hot & previous_hot)
        }

        return changes

    def compare_sector_flow(self, current_sector, previous_sector):
        """对比行业板块资金流向变化"""
        changes = {}

        # 获取当前和之前的热门行业
        current_hot = set()
        previous_hot = set()

        if current_sector['top_inflow']:
            current_hot = set([sector.get('名称', sector.get('code', '')) for sector in current_sector['top_inflow'][:10]])
        if previous_sector['top_inflow']:
            previous_hot = set([sector.get('名称', sector.get('code', '')) for sector in previous_sector['top_inflow'][:10]])

        changes['hot_sectors'] = {
            'new_hot': list(current_hot - previous_hot),
            'lost_hot': list(previous_hot - current_hot),
            'continued_hot': list(current_hot & previous_hot)
        }

        return changes

    def compare_movers(self, current_movers, previous_movers):
        """对比异动股票变化"""
        current_stocks = set([mover['code'] for mover in current_movers['movers']])
        previous_stocks = set([mover['code'] for mover in previous_movers['movers']])

        # 按异动类型分组
        current_by_type = {}
        previous_by_type = {}

        for mover in current_movers['movers']:
            mover_type = mover['type']
            if mover_type not in current_by_type:
                current_by_type[mover_type] = []
            current_by_type[mover_type].append(mover)

        for mover in previous_movers['movers']:
            mover_type = mover['type']
            if mover_type not in previous_by_type:
                previous_by_type[mover_type] = []
            previous_by_type[mover_type].append(mover)

        type_changes = {}
        all_types = set(current_by_type.keys()) | set(previous_by_type.keys())

        for mover_type in all_types:
            current_codes = set([m['code'] for m in current_by_type.get(mover_type, [])])
            previous_codes = set([m['code'] for m in previous_by_type.get(mover_type, [])])

            new_movers = list(current_codes - previous_codes)
            lost_movers = list(previous_codes - current_codes)

            if new_movers or lost_movers:
                type_changes[mover_type] = {
                    'new_count': len(new_movers),
                    'lost_count': len(lost_movers),
                    'new_movers': new_movers[:5],
                    'lost_movers': lost_movers[:5]
                }

        return {
            'total_new': len(current_stocks - previous_stocks),
            'total_lost': len(previous_stocks - current_stocks),
            'type_changes': type_changes
        }

    def generate_time_summary(self, time_key, analysis, previous_analysis=None):
        """生成时间段摘要"""
        summary = f"\n{'='*60}\n"
        summary += f"时间段: {time_key}\n"
        summary += f"{'='*60}\n"

        # 涨停池摘要
        if 'zt_pool' in analysis['details']:
            zt_data = analysis['details']['zt_pool']
            summary += f"\n📈 涨停池情况:\n"
            summary += f"  - 涨停股票数量: {zt_data['stats']['total_zt']}只\n"
            if zt_data['stats']['industries']:
                top_industry = max(zt_data['stats']['industries'].items(), key=lambda x: x[1])
                summary += f"  - 热门行业: {top_industry[0]} ({top_industry[1]}只)\n"

            if zt_data['zt_stocks']:
                summary += f"  - 代表股票: "
                for i, stock in enumerate(zt_data['zt_stocks'][:3]):
                    summary += f"{stock['name']}({stock['code']}) "
                summary += "\n"

        # 资金流向摘要
        if 'fund_flow' in analysis['details']:
            flow_data = analysis['details']['fund_flow']
            summary += f"\n💰 资金流向:\n"
            if flow_data['top_inflow']:
                summary += f"  - 主力净流入前3: "
                for i, stock in enumerate(flow_data['top_inflow'][:3]):
                    if '名称' in stock:
                        summary += f"{stock['名称']} "
                summary += "\n"

        # 新闻摘要
        if 'news' in analysis['details']:
            news_data = analysis['details']['news']
            summary += f"\n📰 重要新闻:\n"
            for i, news in enumerate(news_data['news_list'][:3]):
                if news['title'] and isinstance(news['title'], str) and news['title'].strip():
                    summary += f"  - {news['title'][:50]}...\n"

        # 异动股票
        if 'movers' in analysis['details']:
            movers_data = analysis['details']['movers']
            summary += f"\n🚀 异动股票:\n"
            mover_types = {}
            for mover in movers_data['movers']:
                mover_type = mover['type']
                if mover_type not in mover_types:
                    mover_types[mover_type] = []
                mover_types[mover_type].append(mover['name'])

            for mover_type, stocks in mover_types.items():
                summary += f"  - {mover_type}: {len(stocks)}只 ({', '.join(stocks[:3])}...)\n"

        # 概念板块资金流向
        if 'concept_flow' in analysis['details']:
            concept_data = analysis['details']['concept_flow']
            summary += f"\n🎯 概念板块资金流向:\n"
            if concept_data['top_inflow']:
                summary += f"  - 资金流入前3: "
                for i, concept in enumerate(concept_data['top_inflow'][:3]):
                    name_key = '名称' if '名称' in concept else 'code'
                    net_key = [k for k in concept.keys() if '净' in k and '流入' in k][0] if [k for k in concept.keys() if '净' in k and '流入' in k] else list(concept.keys())[-1]
                    try:
                        flow_amount = f"({float(concept[net_key])/10000:.0f}万)" if concept[net_key] and concept[net_key] != 0 else ""
                    except:
                        flow_amount = ""
                    summary += f"{concept[name_key]}{flow_amount} "
                summary += "\n"

        # 行业板块资金流向
        if 'sector_flow' in analysis['details']:
            sector_data = analysis['details']['sector_flow']
            summary += f"\n🏭 行业板块资金流向:\n"
            if sector_data['top_inflow']:
                summary += f"  - 资金流入前3: "
                for i, sector in enumerate(sector_data['top_inflow'][:3]):
                    name_key = '名称' if '名称' in sector else 'code'
                    net_key = [k for k in sector.keys() if '净' in k and '流入' in k][0] if [k for k in sector.keys() if '净' in k and '流入' in k] else list(sector.keys())[-1]
                    try:
                        flow_amount = f"({float(sector[net_key])/10000:.0f}万)" if sector[net_key] and sector[net_key] != 0 else ""
                    except:
                        flow_amount = ""
                    summary += f"{sector[name_key]}{flow_amount} "
                summary += "\n"

        # 深度变化对比
        if previous_analysis:
            changes = self.compare_time_periods(analysis, previous_analysis)
            if changes:
                summary += f"\n🔄 深度变化分析:\n"

                # 涨停池深度分析
                if 'zt_changes' in changes:
                    zt_changes = changes['zt_changes']
                    summary += f"  📈 涨停池变化:\n"
                    summary += f"    - 总体变化: {zt_changes['summary']['total_change']:+d}只\n"

                    if zt_changes['new_zt_details']:
                        summary += f"    - 新增涨停({len(zt_changes['new_zt_details'])}只): "
                        for stock in zt_changes['new_zt_details'][:3]:
                            board_info = f"({stock['board_count']}板)" if stock['board_count'] > 1 else ""
                            summary += f"{stock['name']}{board_info} "
                        summary += "\n"

                    if zt_changes['lost_zt_details']:
                        summary += f"    - 失去涨停({len(zt_changes['lost_zt_details'])}只): "
                        for stock in zt_changes['lost_zt_details'][:3]:
                            summary += f"{stock['name']} "
                        summary += "\n"

                    if zt_changes['board_count_changes']:
                        summary += f"    - 连板数变化: "
                        for stock in zt_changes['board_count_changes'][:2]:
                            summary += f"{stock['name']}({stock['previous_board']}→{stock['current_board']}板) "
                        summary += "\n"

                    if zt_changes['industry_changes']:
                        summary += f"    - 热门行业变化: "
                        for industry, change in list(zt_changes['industry_changes'].items())[:2]:
                            summary += f"{industry}({change['previous']}→{change['current']}) "
                        summary += "\n"

                # 资金流向深度分析
                if 'flow_changes' in changes:
                    flow_changes = changes['flow_changes']
                    summary += f"  💰 资金流向变化:\n"

                    if flow_changes['new_top100']:
                        summary += f"    - 新进前100名({len(flow_changes['new_top100'])}只): "
                        for stock in flow_changes['new_top100'][:3]:
                            summary += f"{stock['name']} "
                        summary += "\n"

                    if flow_changes['flow_surge_stocks']:
                        summary += f"    - 资金流入激增: "
                        for stock in flow_changes['flow_surge_stocks'][:2]:
                            summary += f"{stock['name']}(+{stock['change_ratio']:.1%}) "
                        summary += "\n"

                    if flow_changes['ranking_changes']:
                        summary += f"    - 排名大幅变化: "
                        for stock in flow_changes['ranking_changes'][:2]:
                            direction = "↑" if stock['rank_change'] > 0 else "↓"
                            summary += f"{stock['name']}({direction}{abs(stock['rank_change'])}位) "
                        summary += "\n"

                # 概念板块变化
                if 'concept_changes' in changes:
                    concept_changes = changes['concept_changes']
                    if concept_changes['hot_concepts']['new_hot']:
                        summary += f"  🎯 概念板块变化:\n"
                        summary += f"    - 新进热门概念: {', '.join(concept_changes['hot_concepts']['new_hot'][:3])}\n"
                    if concept_changes['hot_concepts']['lost_hot']:
                        summary += f"    - 退出热门概念: {', '.join(concept_changes['hot_concepts']['lost_hot'][:3])}\n"

                # 行业板块变化
                if 'sector_changes' in changes:
                    sector_changes = changes['sector_changes']
                    if sector_changes['hot_sectors']['new_hot']:
                        summary += f"  🏭 行业板块变化:\n"
                        summary += f"    - 新进热门行业: {', '.join(sector_changes['hot_sectors']['new_hot'][:3])}\n"
                    if sector_changes['hot_sectors']['lost_hot']:
                        summary += f"    - 退出热门行业: {', '.join(sector_changes['hot_sectors']['lost_hot'][:3])}\n"

                # 异动股票变化
                if 'movers_changes' in changes:
                    movers_changes = changes['movers_changes']
                    if movers_changes['type_changes']:
                        summary += f"  🚀 异动股票变化:\n"
                        for mover_type, change in list(movers_changes['type_changes'].items())[:2]:
                            if change['new_count'] > 0:
                                summary += f"    - {mover_type}: 新增{change['new_count']}只\n"

        return summary

    def generate_daily_report(self):
        """生成完整的日报告"""
        if not self.scan_files():
            return None

        # 按时间排序
        sorted_times = sorted(self.time_data.keys())

        # 分析每个时间段
        time_analyses = {}
        for time_key in sorted_times:
            files_dict = self.time_data[time_key]
            analysis = self.analyze_time_period(time_key, files_dict)
            time_analyses[time_key] = analysis

        # 生成报告
        report = f"""
{'='*80}
                    {self.analysis_date} 股市数据分析报告
{'='*80}

📊 数据概览:
- 分析日期: {self.analysis_date}
- 数据时间段: {sorted_times[0]} - {sorted_times[-1]}
- 总时间点: {len(sorted_times)}个
- 数据文件总数: {sum(len(files) for files in self.time_data.values())}个

"""

        # 添加每个时间段的详细分析
        previous_analysis = None
        for i, time_key in enumerate(sorted_times):
            analysis = time_analyses[time_key]
            time_summary = self.generate_time_summary(time_key, analysis, previous_analysis)
            report += time_summary
            previous_analysis = analysis

        # 添加全天总结
        report += self.generate_daily_summary(time_analyses, sorted_times)

        return report

    def generate_daily_summary(self, time_analyses, sorted_times):
        """生成全天总结"""
        summary = f"\n\n{'='*80}\n"
        summary += f"                           全天总结\n"
        summary += f"{'='*80}\n"

        # 统计全天涨停股票
        all_zt_stocks = set()
        max_zt_count = 0
        max_zt_time = ""

        for time_key, analysis in time_analyses.items():
            if 'zt_pool' in analysis['details']:
                zt_data = analysis['details']['zt_pool']
                current_zt = set([stock['code'] for stock in zt_data['zt_stocks']])
                all_zt_stocks.update(current_zt)

                if zt_data['stats']['total_zt'] > max_zt_count:
                    max_zt_count = zt_data['stats']['total_zt']
                    max_zt_time = time_key

        summary += f"\n📈 涨停池统计:\n"
        summary += f"  - 全天涨停股票总数: {len(all_zt_stocks)}只\n"
        summary += f"  - 最高涨停数量: {max_zt_count}只 (时间: {max_zt_time})\n"

        # 统计活跃时间段
        active_periods = []
        for time_key, analysis in time_analyses.items():
            activity_score = 0
            if 'zt_pool' in analysis['details']:
                activity_score += analysis['details']['zt_pool']['stats']['total_zt']
            if 'news' in analysis['details']:
                activity_score += len(analysis['details']['news']['news_list'])
            if 'movers' in analysis['details']:
                activity_score += len(analysis['details']['movers']['movers'])

            active_periods.append((time_key, activity_score))

        # 排序找出最活跃的时间段
        active_periods.sort(key=lambda x: x[1], reverse=True)

        summary += f"\n🔥 最活跃时间段:\n"
        for i, (time_key, score) in enumerate(active_periods[:5]):
            summary += f"  {i+1}. {time_key} (活跃度: {score})\n"

        summary += f"\n📝 分析建议:\n"
        summary += f"  - 关注{max_zt_time}时段的市场动向，涨停股票最多\n"
        summary += f"  - 重点关注活跃时间段的资金流向变化\n"
        summary += f"  - 建议复盘全天{len(all_zt_stocks)}只涨停股票的表现\n"

        return summary

    def save_report(self, report):
        """保存报告到文件"""
        if not os.path.exists(OUTPUT_DIR):
            os.makedirs(OUTPUT_DIR)

        filename = f"{OUTPUT_DIR}/fund_analysis_report_{self.analysis_date}.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"报告已保存到: {filename}")
        return filename


def main():
    """主函数"""
    print(f"开始分析 {ANALYSIS_DATE} 的基金数据...")

    analyzer = FundDataAnalyzer(ANALYSIS_DATE, FUND_DATA_DIR)
    report = analyzer.generate_daily_report()

    if report:
        print(report)
        analyzer.save_report(report)
        print(f"\n✅ 分析完成！")
    else:
        print("❌ 分析失败，请检查数据文件夹是否存在")


if __name__ == "__main__":
    main()
