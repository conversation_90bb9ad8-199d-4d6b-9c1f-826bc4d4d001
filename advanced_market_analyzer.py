#!/usr/bin/.env python3
# -*- coding: utf-8 -*-
"""
高级市场异常分析器
专门用于发现市场中的异常现象、趋势变化和投资机会
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import glob
import re
from collections import defaultdict, Counter
import warnings
warnings.filterwarnings('ignore')

# 配置参数
ANALYSIS_DATE = "2025-07-14"  # 要分析的日期
FUND_DATA_DIR = "../fund_data"
OUTPUT_DIR = "../reports"

class AdvancedMarketAnalyzer:
    def __init__(self, analysis_date, data_dir):
        self.analysis_date = analysis_date
        self.data_dir = data_dir
        self.date_dir = os.path.join(data_dir, analysis_date)
        self.time_data = defaultdict(dict)
        
    def load_csv_safe(self, filepath):
        """安全加载CSV文件"""
        try:
            for encoding in ['utf-8', 'gbk', 'gb2312']:
                try:
                    df = pd.read_csv(filepath, encoding=encoding)
                    return df
                except UnicodeDecodeError:
                    continue
            return None
        except Exception as e:
            return None
    
    def scan_and_load_data(self):
        """扫描并加载所有数据"""
        if not os.path.exists(self.date_dir):
            print(f"错误：日期文件夹 {self.date_dir} 不存在")
            return False
            
        files = os.listdir(self.date_dir)
        
        for file in files:
            if file.endswith('.csv'):
                time_match = re.search(r'(\d{2})-(\d{2})_', file)
                if time_match:
                    hour, minute = time_match.groups()
                    time_key = f"{hour}:{minute}"
                    
                    filepath = os.path.join(self.date_dir, file)
                    df = self.load_csv_safe(filepath)
                    
                    if df is not None and not df.empty:
                        if time_key not in self.time_data:
                            self.time_data[time_key] = {}
                        
                        # 根据文件类型存储数据
                        if 'zt_pool' in file:
                            self.time_data[time_key]['zt_pool'] = df
                        elif 'fund_flow_tpdog' in file:
                            self.time_data[time_key]['fund_flow'] = df
                        elif 'concept_fund_flow' in file:
                            self.time_data[time_key]['concept_flow'] = df
                        elif 'sector_fund_flow' in file:
                            self.time_data[time_key]['sector_flow'] = df
                        elif 'news' in file:
                            if 'news' not in self.time_data[time_key]:
                                self.time_data[time_key]['news'] = []
                            self.time_data[time_key]['news'].append(df)
        
        return True
    
    def detect_zt_anomalies(self):
        """检测涨停池异常现象"""
        anomalies = []
        sorted_times = sorted(self.time_data.keys())
        
        for i, time_key in enumerate(sorted_times):
            if 'zt_pool' not in self.time_data[time_key]:
                continue
                
            df = self.time_data[time_key]['zt_pool']
            
            # 异常1: 涨停数量突然激增
            if i > 0:
                prev_time = sorted_times[i-1]
                if 'zt_pool' in self.time_data[prev_time]:
                    prev_count = len(self.time_data[prev_time]['zt_pool'])
                    current_count = len(df)
                    
                    if current_count - prev_count >= 10:  # 新增10只以上涨停
                        anomalies.append({
                            'type': '涨停激增',
                            'time': time_key,
                            'description': f'涨停数量从{prev_count}只激增至{current_count}只',
                            'severity': 'high',
                            'details': {
                                'increase': current_count - prev_count,
                                'prev_count': prev_count,
                                'current_count': current_count
                            }
                        })
            
            # 异常2: 特定行业集中涨停
            if '所属行业' in df.columns:
                industry_counts = df['所属行业'].value_counts()
                for industry, count in industry_counts.items():
                    if count >= 5:  # 单个行业5只以上涨停
                        anomalies.append({
                            'type': '行业集中涨停',
                            'time': time_key,
                            'description': f'{industry}行业有{count}只股票涨停',
                            'severity': 'medium',
                            'details': {
                                'industry': industry,
                                'count': count,
                                'stocks': df[df['所属行业'] == industry]['名称'].tolist()[:5]
                            }
                        })
            
            # 异常3: 高连板股票出现
            if '连板数' in df.columns:
                high_board_stocks = df[df['连板数'] >= 5]
                if not high_board_stocks.empty:
                    for _, stock in high_board_stocks.iterrows():
                        anomalies.append({
                            'type': '高连板股票',
                            'time': time_key,
                            'description': f'{stock["名称"]}达到{stock["连板数"]}连板',
                            'severity': 'high',
                            'details': {
                                'stock_name': stock['名称'],
                                'stock_code': stock['代码'],
                                'board_count': stock['连板数'],
                                'industry': stock.get('所属行业', '未知')
                            }
                        })
        
        return anomalies
    
    def detect_fund_flow_anomalies(self):
        """检测资金流向异常"""
        anomalies = []
        sorted_times = sorted(self.time_data.keys())
        
        for i, time_key in enumerate(sorted_times):
            if 'fund_flow' not in self.time_data[time_key]:
                continue
                
            df = self.time_data[time_key]['fund_flow']
            
            # 寻找净流入列
            net_cols = [col for col in df.columns if '净' in col and '流入' in col]
            if not net_cols:
                continue
                
            net_col = net_cols[0]
            df[net_col] = pd.to_numeric(df[net_col], errors='coerce')
            
            # 异常1: 超大单股票净流入
            if '代码' in df.columns and '名称' in df.columns:
                mega_inflow = df[df[net_col] > 100000000]  # 净流入超过1亿
                for _, stock in mega_inflow.iterrows():
                    anomalies.append({
                        'type': '超大资金流入',
                        'time': time_key,
                        'description': f'{stock["名称"]}净流入{stock[net_col]/100000000:.1f}亿',
                        'severity': 'high',
                        'details': {
                            'stock_name': stock['名称'],
                            'stock_code': stock['代码'],
                            'net_flow': stock[net_col],
                            'flow_amount_yi': stock[net_col]/100000000
                        }
                    })
            
            # 异常2: 资金流向突然逆转
            if i > 0:
                prev_time = sorted_times[i-1]
                if 'fund_flow' in self.time_data[prev_time]:
                    prev_df = self.time_data[prev_time]['fund_flow']
                    if net_col in prev_df.columns and '代码' in prev_df.columns:
                        prev_df[net_col] = pd.to_numeric(prev_df[net_col], errors='coerce')
                        
                        # 合并数据进行对比
                        current_cols = ['代码', '名称', net_col] if '名称' in df.columns else ['代码', net_col]
                        prev_cols = ['代码', net_col]

                        # 确保列存在
                        current_cols = [col for col in current_cols if col in df.columns]
                        prev_cols = [col for col in prev_cols if col in prev_df.columns]

                        if '代码' not in current_cols or '代码' not in prev_cols:
                            continue

                        merged = pd.merge(df[current_cols],
                                        prev_df[prev_cols],
                                        on='代码', suffixes=('_current', '_prev'))
                        
                        # 检测流向逆转
                        merged['flow_change'] = merged[f'{net_col}_current'] - merged[f'{net_col}_prev']
                        merged['flow_ratio'] = merged['flow_change'] / (merged[f'{net_col}_prev'].abs() + 1)
                        
                        # 资金流向剧烈变化（变化幅度超过200%且金额超过5000万）
                        dramatic_changes = merged[
                            (merged['flow_ratio'].abs() > 2.0) & 
                            (merged['flow_change'].abs() > 50000000)
                        ]
                        
                        for _, stock in dramatic_changes.iterrows():
                            direction = "流入" if stock['flow_change'] > 0 else "流出"
                            anomalies.append({
                                'type': '资金流向逆转',
                                'time': time_key,
                                'description': f'{stock["名称"]}资金{direction}剧变{stock["flow_change"]/10000:.0f}万',
                                'severity': 'medium',
                                'details': {
                                    'stock_name': stock['名称'],
                                    'stock_code': stock['代码'],
                                    'flow_change': stock['flow_change'],
                                    'flow_ratio': stock['flow_ratio'],
                                    'prev_flow': stock[f'{net_col}_prev'],
                                    'current_flow': stock[f'{net_col}_current']
                                }
                            })
        
        return anomalies
    
    def detect_concept_sector_anomalies(self):
        """检测概念板块和行业板块异常"""
        anomalies = []
        sorted_times = sorted(self.time_data.keys())
        
        for time_key in sorted_times:
            # 检测概念板块异常
            if 'concept_flow' in self.time_data[time_key]:
                df = self.time_data[time_key]['concept_flow']
                net_cols = [col for col in df.columns if '净' in col and '流入' in col]
                
                if net_cols and '板块名称' in df.columns:
                    net_col = net_cols[0]
                    df[net_col] = pd.to_numeric(df[net_col], errors='coerce')
                    
                    # 概念板块资金大幅流入
                    hot_concepts = df[df[net_col] > 50000000]  # 净流入超过5000万
                    for _, concept in hot_concepts.iterrows():
                        anomalies.append({
                            'type': '概念板块热点',
                            'time': time_key,
                            'description': f'{concept["板块名称"]}概念净流入{concept[net_col]/10000:.0f}万',
                            'severity': 'medium',
                            'details': {
                                'concept_name': concept['板块名称'],
                                'net_flow': concept[net_col],
                                'flow_amount_wan': concept[net_col]/10000
                            }
                        })
            
            # 检测行业板块异常
            if 'sector_flow' in self.time_data[time_key]:
                df = self.time_data[time_key]['sector_flow']
                net_cols = [col for col in df.columns if '净' in col and '流入' in col]
                
                if net_cols and '板块名称' in df.columns:
                    net_col = net_cols[0]
                    df[net_col] = pd.to_numeric(df[net_col], errors='coerce')
                    
                    # 行业板块资金大幅流入
                    hot_sectors = df[df[net_col] > 100000000]  # 净流入超过1亿
                    for _, sector in hot_sectors.iterrows():
                        anomalies.append({
                            'type': '行业板块热点',
                            'time': time_key,
                            'description': f'{sector["板块名称"]}行业净流入{sector[net_col]/100000000:.1f}亿',
                            'severity': 'high',
                            'details': {
                                'sector_name': sector['板块名称'],
                                'net_flow': sector[net_col],
                                'flow_amount_yi': sector[net_col]/100000000
                            }
                        })
        
        return anomalies

    def detect_news_driven_events(self):
        """检测新闻驱动的市场事件"""
        events = []
        sorted_times = sorted(self.time_data.keys())

        # 关键词映射
        keywords_map = {
            'AI人工智能': ['AI', '人工智能', '大模型', 'GPT', '算力', '芯片'],
            '新能源': ['新能源', '锂电', '光伏', '风电', '储能', '电池'],
            '医药生物': ['医药', '疫苗', '新药', '生物', '医疗', '健康'],
            '军工国防': ['军工', '国防', '航天', '导弹', '雷达', '军事'],
            '房地产': ['房地产', '地产', '房价', '楼市', '土地'],
            '金融': ['银行', '保险', '券商', '金融', '货币', '利率'],
            '消费': ['消费', '零售', '食品', '饮料', '服装', '家电'],
            '科技': ['科技', '5G', '半导体', '通信', '互联网', '软件']
        }

        for time_key in sorted_times:
            if 'news' not in self.time_data[time_key]:
                continue

            news_dfs = self.time_data[time_key]['news']
            all_news = []

            for df in news_dfs:
                if '标题' in df.columns:
                    for _, row in df.iterrows():
                        title = str(row.get('标题', ''))
                        content = str(row.get('内容', ''))
                        if title and title != 'nan':
                            all_news.append(title + ' ' + content)

            # 分析新闻关键词
            for category, keywords in keywords_map.items():
                relevant_news = []
                for news in all_news:
                    if any(keyword in news for keyword in keywords):
                        relevant_news.append(news[:100] + '...')

                if len(relevant_news) >= 3:  # 该类别新闻数量较多
                    events.append({
                        'type': '新闻热点',
                        'time': time_key,
                        'description': f'{category}相关新闻密集出现({len(relevant_news)}条)',
                        'severity': 'medium',
                        'details': {
                            'category': category,
                            'news_count': len(relevant_news),
                            'sample_news': relevant_news[:3]
                        }
                    })

        return events

    def find_trading_opportunities(self):
        """发现交易机会"""
        opportunities = []
        sorted_times = sorted(self.time_data.keys())

        # 机会1: 涨停+资金大幅流入的股票
        for time_key in sorted_times:
            zt_stocks = set()
            if 'zt_pool' in self.time_data[time_key]:
                zt_df = self.time_data[time_key]['zt_pool']
                if '代码' in zt_df.columns:
                    zt_stocks = set(zt_df['代码'].astype(str))

            if 'fund_flow' in self.time_data[time_key]:
                flow_df = self.time_data[time_key]['fund_flow']
                net_cols = [col for col in flow_df.columns if '净' in col and '流入' in col]

                if net_cols and '代码' in flow_df.columns:
                    net_col = net_cols[0]
                    flow_df[net_col] = pd.to_numeric(flow_df[net_col], errors='coerce')

                    # 涨停且资金大幅流入的股票
                    for _, stock in flow_df.iterrows():
                        code = str(stock['代码'])
                        if code in zt_stocks and stock[net_col] > 30000000:  # 涨停+净流入超过3000万
                            opportunities.append({
                                'type': '强势涨停股',
                                'time': time_key,
                                'description': f'{stock["名称"]}涨停+资金流入{stock[net_col]/10000:.0f}万',
                                'severity': 'high',
                                'details': {
                                    'stock_name': stock['名称'],
                                    'stock_code': code,
                                    'net_flow': stock[net_col],
                                    'is_zt': True
                                }
                            })

        # 机会2: 连续多个时段资金流入的股票
        continuous_inflow = defaultdict(list)

        for time_key in sorted_times:
            if 'fund_flow' in self.time_data[time_key]:
                flow_df = self.time_data[time_key]['fund_flow']
                net_cols = [col for col in flow_df.columns if '净' in col and '流入' in col]

                if net_cols and '代码' in flow_df.columns:
                    net_col = net_cols[0]
                    flow_df[net_col] = pd.to_numeric(flow_df[net_col], errors='coerce')

                    # 记录资金流入前20的股票
                    top_inflow = flow_df.nlargest(20, net_col)
                    for _, stock in top_inflow.iterrows():
                        code = str(stock['代码'])
                        continuous_inflow[code].append({
                            'time': time_key,
                            'name': stock['名称'],
                            'flow': stock[net_col]
                        })

        # 找出连续3个以上时段都有资金流入的股票
        for code, records in continuous_inflow.items():
            if len(records) >= 3:
                total_flow = sum(r['flow'] for r in records)
                opportunities.append({
                    'type': '持续资金关注',
                    'time': f"{records[0]['time']}-{records[-1]['time']}",
                    'description': f'{records[0]["name"]}连续{len(records)}个时段资金流入',
                    'severity': 'medium',
                    'details': {
                        'stock_name': records[0]['name'],
                        'stock_code': code,
                        'continuous_periods': len(records),
                        'total_flow': total_flow,
                        'avg_flow': total_flow / len(records)
                    }
                })

        return opportunities

    def generate_anomaly_report(self):
        """生成异常分析报告"""
        if not self.scan_and_load_data():
            return None

        print("正在检测市场异常...")

        # 检测各类异常
        zt_anomalies = self.detect_zt_anomalies()
        flow_anomalies = self.detect_fund_flow_anomalies()
        concept_anomalies = self.detect_concept_sector_anomalies()
        news_events = self.detect_news_driven_events()
        opportunities = self.find_trading_opportunities()

        # 生成报告
        report = f"""
{'='*80}
                    {self.analysis_date} 市场异常分析报告
{'='*80}

📊 分析概览:
- 分析日期: {self.analysis_date}
- 数据时间点: {len(self.time_data)}个
- 检测到异常: {len(zt_anomalies + flow_anomalies + concept_anomalies)}个
- 新闻事件: {len(news_events)}个
- 交易机会: {len(opportunities)}个

"""

        # 涨停池异常
        if zt_anomalies:
            report += f"\n🚨 涨停池异常 ({len(zt_anomalies)}个):\n"
            report += "=" * 50 + "\n"

            for anomaly in sorted(zt_anomalies, key=lambda x: x['time']):
                severity_icon = "🔴" if anomaly['severity'] == 'high' else "🟡"
                report += f"{severity_icon} {anomaly['time']} - {anomaly['type']}\n"
                report += f"   {anomaly['description']}\n"

                if anomaly['type'] == '行业集中涨停':
                    stocks = ', '.join(anomaly['details']['stocks'])
                    report += f"   相关股票: {stocks}\n"
                elif anomaly['type'] == '高连板股票':
                    report += f"   行业: {anomaly['details']['industry']}\n"
                report += "\n"

        # 资金流向异常
        if flow_anomalies:
            report += f"\n💰 资金流向异常 ({len(flow_anomalies)}个):\n"
            report += "=" * 50 + "\n"

            for anomaly in sorted(flow_anomalies, key=lambda x: x['time']):
                severity_icon = "🔴" if anomaly['severity'] == 'high' else "🟡"
                report += f"{severity_icon} {anomaly['time']} - {anomaly['type']}\n"
                report += f"   {anomaly['description']}\n"

                if anomaly['type'] == '资金流向逆转':
                    prev_flow = anomaly['details']['prev_flow'] / 10000
                    current_flow = anomaly['details']['current_flow'] / 10000
                    report += f"   变化: {prev_flow:.0f}万 → {current_flow:.0f}万\n"
                report += "\n"

        # 概念板块异常
        if concept_anomalies:
            report += f"\n🎯 板块异常 ({len(concept_anomalies)}个):\n"
            report += "=" * 50 + "\n"

            for anomaly in sorted(concept_anomalies, key=lambda x: x['time']):
                severity_icon = "🔴" if anomaly['severity'] == 'high' else "🟡"
                report += f"{severity_icon} {anomaly['time']} - {anomaly['type']}\n"
                report += f"   {anomaly['description']}\n\n"

        # 新闻事件
        if news_events:
            report += f"\n📰 新闻热点事件 ({len(news_events)}个):\n"
            report += "=" * 50 + "\n"

            for event in sorted(news_events, key=lambda x: x['time']):
                report += f"📅 {event['time']} - {event['details']['category']}\n"
                report += f"   新闻数量: {event['details']['news_count']}条\n"
                report += f"   样本新闻:\n"
                for news in event['details']['sample_news']:
                    report += f"   • {news}\n"
                report += "\n"

        # 交易机会
        if opportunities:
            report += f"\n🎯 发现的交易机会 ({len(opportunities)}个):\n"
            report += "=" * 50 + "\n"

            # 按严重程度排序
            high_opportunities = [op for op in opportunities if op['severity'] == 'high']
            medium_opportunities = [op for op in opportunities if op['severity'] == 'medium']

            if high_opportunities:
                report += "🔥 高优先级机会:\n"
                for op in sorted(high_opportunities, key=lambda x: x['time']):
                    report += f"   • {op['time']} - {op['description']}\n"
                report += "\n"

            if medium_opportunities:
                report += "⭐ 中等优先级机会:\n"
                for op in sorted(medium_opportunities, key=lambda x: x['time'])[:10]:  # 只显示前10个
                    report += f"   • {op['time']} - {op['description']}\n"
                report += "\n"

        # 总结建议
        report += f"\n📝 分析总结:\n"
        report += "=" * 50 + "\n"

        high_severity_count = len([a for a in zt_anomalies + flow_anomalies + concept_anomalies if a['severity'] == 'high'])

        if high_severity_count > 0:
            report += f"⚠️  发现 {high_severity_count} 个高风险异常，需要重点关注\n"

        if len(opportunities) > 0:
            high_op_count = len([op for op in opportunities if op['severity'] == 'high'])
            report += f"💡 发现 {high_op_count} 个高优先级交易机会\n"

        if len(news_events) > 0:
            hot_categories = Counter([event['details']['category'] for event in news_events])
            top_category = hot_categories.most_common(1)[0] if hot_categories else None
            if top_category:
                report += f"📈 {top_category[0]} 是今日新闻热点，关注相关概念股\n"

        report += f"\n建议重点关注时间段: "
        all_times = set()
        for anomaly in zt_anomalies + flow_anomalies + concept_anomalies:
            if anomaly['severity'] == 'high':
                all_times.add(anomaly['time'])

        if all_times:
            report += ', '.join(sorted(all_times)[:5])
        else:
            report += "市场相对平稳"

        return report

    def save_report(self, report):
        """保存报告到文件"""
        if not os.path.exists(OUTPUT_DIR):
            os.makedirs(OUTPUT_DIR)

        filename = f"{OUTPUT_DIR}/market_anomaly_report_{self.analysis_date}.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"异常分析报告已保存到: {filename}")
        return filename


def main():
    """主函数"""
    print(f"开始进行 {ANALYSIS_DATE} 的高级市场异常分析...")

    analyzer = AdvancedMarketAnalyzer(ANALYSIS_DATE, FUND_DATA_DIR)
    report = analyzer.generate_anomaly_report()

    if report:
        print(report)
        analyzer.save_report(report)
        print(f"\n✅ 异常分析完成！")
    else:
        print("❌ 分析失败，请检查数据文件夹是否存在")


if __name__ == "__main__":
    main()
